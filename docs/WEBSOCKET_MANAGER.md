# WebSocket Manager Implementation

## Overview

The WebSocket Manager is a critical component that solves the issue of multiple code executions not working properly in the BITS JupyterHub frontend. It implements persistent WebSocket connections to Jupyter kernels, ensuring reliable communication and proper session management.

## Problem Statement

### Original Issue
- Each `executeCell` call created a new WebSocket connection
- Connections were immediately closed after execution
- Subsequent executions failed due to session state loss
- Race conditions occurred with rapid cell executions
- Kernel state became inconsistent

### Root Cause
```typescript
// OLD IMPLEMENTATION - Problematic
static async executeCell(kernelId: string, cellId: string, code: string) {
  const ws = new WebSocket(wsUrl) // New connection each time
  // ... execute code ...
  ws.close() // Connection closed after execution
}
```

## Solution Architecture

### WebSocketManager Class

The `WebSocketManager` implements a singleton pattern to manage persistent connections per kernel.

```typescript
class WebSocketManager {
  private connections: Map<string, {
    ws: WebSocket | null
    isConnecting: boolean
    messageQueue: ExecutionRequest[]
    messageHandlers: Map<string, (result: any) => void>
    executionCount: number
  }> = new Map()
}
```

### Key Components

#### 1. Connection Management
- **Persistent Connections**: One WebSocket per kernel ID
- **Connection Pooling**: Reuses existing connections
- **State Tracking**: Monitors connection status (connected/connecting/disconnected)

#### 2. Message Handling
- **Message Queuing**: Queues requests when connection is establishing
- **Handler Registration**: Maps message IDs to response handlers
- **Execution Count Tracking**: Maintains proper kernel state

#### 3. Error Handling
- **Automatic Reconnection**: Handles connection failures gracefully
- **Timeout Management**: Prevents hanging requests
- **Fallback Mechanisms**: Provides mock execution when needed

## Implementation Details

### Connection Lifecycle

```typescript
async connectToKernel(kernelId: string): Promise<boolean> {
  // 1. Check if connection already exists
  if (existing?.ws?.readyState === WebSocket.OPEN) {
    return true
  }

  // 2. Handle connection in progress
  if (existing?.isConnecting) {
    return new Promise((resolve) => {
      // Wait for connection to complete
    })
  }

  // 3. Create new connection
  const ws = new WebSocket(wsUrl)
  // Set up event handlers
}
```

### Message Processing

```typescript
private handleMessage(kernelId: string, data: string) {
  const message: WebSocketMessage = JSON.parse(data)
  const msgId = message.header.msg_id
  const handler = connection.messageHandlers.get(msgId)
  
  if (handler) {
    handler(message) // Process response
    connection.messageHandlers.delete(msgId) // Clean up
  }
}
```

### Execution Request Flow

```typescript
async executeCode(kernelId: string, cellId: string, code: string) {
  // 1. Ensure connection
  const isConnected = await this.connectToKernel(kernelId)
  
  // 2. Create execution request
  return new Promise((resolve, reject) => {
    const request: ExecutionRequest = {
      code, cellId, resolve, reject
    }
    this.sendExecuteRequest(kernelId, request)
  })
}
```

## API Reference

### WebSocketManager Methods

#### `connectToKernel(kernelId: string): Promise<boolean>`
Establishes a persistent WebSocket connection to the specified kernel.

**Parameters:**
- `kernelId`: Unique identifier for the Jupyter kernel

**Returns:**
- `Promise<boolean>`: True if connection successful, false otherwise

**Example:**
```typescript
const isConnected = await webSocketManager.connectToKernel('kernel-123')
```

#### `executeCode(kernelId: string, cellId: string, code: string): Promise<any>`
Executes Python code on the specified kernel using the persistent connection.

**Parameters:**
- `kernelId`: Kernel identifier
- `cellId`: Cell identifier for tracking
- `code`: Python code to execute

**Returns:**
- `Promise<any>`: Execution result with outputs and status

**Example:**
```typescript
const result = await webSocketManager.executeCode(
  'kernel-123', 
  'cell-456', 
  'print("Hello, World!")'
)
```

#### `disconnectFromKernel(kernelId: string): void`
Closes the WebSocket connection and cleans up resources.

**Parameters:**
- `kernelId`: Kernel identifier to disconnect from

**Example:**
```typescript
webSocketManager.disconnectFromKernel('kernel-123')
```

#### `isConnected(kernelId: string): boolean`
Checks if a WebSocket connection is currently open.

**Parameters:**
- `kernelId`: Kernel identifier

**Returns:**
- `boolean`: True if connected, false otherwise

#### `getConnectionStatus(kernelId: string): 'connected' | 'connecting' | 'disconnected'`
Gets the current connection status for a kernel.

**Parameters:**
- `kernelId`: Kernel identifier

**Returns:**
- Connection status string

## Integration with JupyterAPI

### Updated executeCell Method

```typescript
static async executeCell(kernelId: string, cellId: string, code: string): Promise<any> {
  try {
    await this.ensureXSRFToken()
    
    let token = this.getToken()
    if (!token) {
      await this.extractTokenFromPage()
      token = this.getToken()
    }
    
    if (!token) {
      // Fallback to mock execution
      return this.generateMockOutputs(code)
    }
    
    // Use WebSocket manager for persistent connections
    return await webSocketManager.executeCode(kernelId, cellId, code)
    
  } catch (error: any) {
    console.error('Error executing cell:', error)
    return this.generateMockOutputs(code)
  }
}
```

## UI Integration

### Status Indicators

The CustomNotebook component now displays WebSocket connection status:

```typescript
const [wsConnectionStatus, setWsConnectionStatus] = useState<
  'connected' | 'connecting' | 'disconnected'
>('disconnected')
```

### Visual Feedback

```tsx
<div className="flex items-center space-x-2">
  <span>Python 3 (ipykernel) | {kernelStatus}</span>
  <div className={`w-2 h-2 rounded-full ${
    wsConnectionStatus === 'connected' ? 'bg-green-500' : 
    wsConnectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
  }`} title={`WebSocket: ${wsConnectionStatus}`}></div>
</div>
```

### Cleanup on Unmount

```typescript
useEffect(() => {
  checkConnectionAndStartKernel()
  
  return () => {
    if (kernelId) {
      webSocketManager.disconnectFromKernel(kernelId)
    }
  }
}, [])
```

## Message Protocol

### Jupyter Kernel Messaging Protocol

The WebSocket Manager implements the Jupyter kernel messaging protocol:

#### Execute Request
```typescript
const executeRequest: WebSocketMessage = {
  header: {
    msg_id: msgId,
    msg_type: 'execute_request',
    username: 'user',
    session: kernelId,
    date: new Date().toISOString(),
    version: '5.3'
  },
  parent_header: {},
  metadata: {},
  content: {
    code: request.code,
    silent: false,
    store_history: true,
    user_expressions: {},
    allow_stdin: false,
    stop_on_error: true
  }
}
```

#### Message Types Handled
- `execute_reply`: Kernel execution response
- `stream`: Standard output/error streams
- `execute_result`: Code execution results
- `display_data`: Rich output (plots, images)
- `error`: Execution errors
- `status`: Kernel status updates

## Error Handling

### Connection Failures
- Automatic timeout handling (5 seconds)
- Graceful fallback to mock execution
- Connection state cleanup

### Message Processing Errors
- JSON parsing error handling
- Invalid message type handling
- Missing handler cleanup

### Timeout Management
- 10-second execution timeout
- Connection establishment timeout
- Automatic cleanup of hanging requests

## Performance Benefits

### Before (Multiple Connections)
- ❌ New WebSocket per execution
- ❌ Connection overhead
- ❌ Session state loss
- ❌ Race conditions

### After (Persistent Connections)
- ✅ Single WebSocket per kernel
- ✅ Connection reuse
- ✅ Session state preservation
- ✅ Reliable execution

## Testing

### Manual Testing Steps
1. Start JupyterHub server
2. Configure API token in frontend
3. Create multiple code cells
4. Execute cells sequentially
5. Verify WebSocket status indicators
6. Test rapid execution scenarios

### Expected Behavior
- First execution establishes connection
- Subsequent executions reuse connection
- Status bar shows green connection indicator
- No connection errors in console
- Proper execution count tracking

## Troubleshooting

### Common Issues

#### Connection Timeout
**Symptoms:** Yellow connection indicator, execution delays
**Solution:** Check JupyterHub server status and token validity

#### Message Queue Overflow
**Symptoms:** Executions not completing
**Solution:** Check for rapid execution patterns, implement rate limiting if needed

#### Memory Leaks
**Symptoms:** Increasing memory usage
**Solution:** Ensure proper cleanup in component unmount

### Debug Information
```typescript
// Enable debug logging
console.log('WebSocket connection status:', wsConnectionStatus)
console.log('Kernel execution count:', executionCount)
console.log('Message queue length:', messageQueue.length)
```

## Future Enhancements

### Planned Improvements
- **Connection pooling** for multiple kernels
- **Automatic reconnection** with exponential backoff
- **Message compression** for large outputs
- **Rate limiting** for rapid executions
- **Connection health monitoring**

### Scalability Considerations
- **Load balancing** across multiple kernels
- **Connection limits** per user
- **Resource cleanup** strategies
- **Performance monitoring** and metrics

## Conclusion

The WebSocket Manager implementation provides a robust, scalable solution for persistent kernel communication. It eliminates the multiple execution issues while maintaining clean separation of concerns and proper error handling.

The implementation follows Jupyter protocol standards and integrates seamlessly with the existing frontend architecture, providing users with a reliable and responsive notebook experience.
