# Quick Setup Guide

## 🚀 Get Started in 5 Minutes

This guide will help you set up and start using the BITS JupyterHub frontend quickly.

## Prerequisites

Before you begin, make sure you have:

- ✅ **Node.js** (v16 or higher) installed
- ✅ **JupyterHub** server running on `http://localhost:8888`
- ✅ **Git** installed (for cloning the repository)

## Step 1: Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd bits-hub-frontend

# Install dependencies
npm install
```

## Step 2: Start the Frontend

```bash
# Start the development server
npm run dev
```

You should see output like:
```
  VITE v4.x.x  ready in xxx ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: use --host to expose
```

## Step 3: Open the Application

1. **Open your browser** and go to `http://localhost:3000`
2. **You'll see the dashboard** with a QuickSetup component

## Step 4: Configure Your JupyterHub Token

### Option A: Quick Setup (Recommended)

1. **Click "Open JupyterHub"** in the QuickSetup section
2. **Copy the token** from the URL (after `?token=`)
3. **Click "Configure Token"** in the QuickSetup
4. **Paste your token** and click "Set Token"

### Option B: Manual Setup

1. **Click "Manage Token"** in the header
2. **Follow the step-by-step instructions**
3. **Test your token** to ensure it works

## Step 5: Start Coding!

1. **Navigate to the notebook** by clicking "New Notebook"
2. **Create your first cell** and write some Python code:

```python
print("Hello from BITS!")
import numpy as np
arr = np.array([1, 2, 3, 4, 5])
print(f"Array: {arr}")
print(f"Sum: {arr.sum()}")
```

3. **Click the play button** to execute your code
4. **See the results** appear below the cell

## ✅ Verification Checklist

- [ ] Frontend loads at `http://localhost:3000`
- [ ] QuickSetup component is visible
- [ ] Token is configured successfully
- [ ] Status indicator shows green (connected)
- [ ] Code execution works
- [ ] Multiple cells can be executed

## 🎯 What You Can Do Now

### Basic Notebook Operations
- ✅ **Create cells**: Click the "+" button
- ✅ **Execute code**: Click play button or press Shift+Enter
- ✅ **Switch modes**: Code/Markdown toggle
- ✅ **Delete cells**: Click trash icon
- ✅ **Save notebook**: Click save button

### Advanced Features
- ✅ **Multiple executions**: Execute cells in sequence
- ✅ **Real-time output**: See results immediately
- ✅ **Error handling**: Clear error messages
- ✅ **Status monitoring**: Connection status indicators

## 🔧 Troubleshooting

### Common Issues

#### "No API token available"
**Quick Fix:**
1. Click "Manage Token" in header
2. Follow the setup guide
3. Copy token from JupyterHub URL

#### Frontend won't start
**Quick Fix:**
```bash
# Check Node.js version
node --version

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Code execution not working
**Quick Fix:**
1. Check the status indicator (should be green)
2. Verify JupyterHub is running on port 8888
3. Test token validity in TokenManager

### Still Having Issues?

1. **Check the console** for error messages
2. **Verify JupyterHub** is running and accessible
3. **Test the token** using the TokenManager
4. **Restart both** JupyterHub and the frontend

## 📚 Next Steps

### Learn More
- Read the [WebSocket Manager Documentation](docs/WEBSOCKET_MANAGER.md)
- Explore the [Token Management System](docs/TOKEN_MANAGEMENT.md)
- Check the [Full README](README.md)

### Advanced Usage
- **File Management**: Upload and manage datasets
- **Terminal Access**: Use the built-in terminal
- **Customization**: Modify the BITS branding
- **Development**: Contribute to the project

## 🆘 Need Help?

- **Documentation**: Check the `docs/` folder
- **Issues**: Open a GitHub issue
- **Support**: Contact the development team

---

**Happy Coding! 🎉**

*Built with ❤️ for BITS Pilani*
