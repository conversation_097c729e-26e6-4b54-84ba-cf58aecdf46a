# BITS JupyterHub Frontend

A modern, custom React frontend for JupyterHub that provides a BITS-branded interface with enhanced user experience and persistent WebSocket connections for reliable code execution.

## 🚀 Features

### Core Functionality
- **Custom Notebook Interface**: Modern React-based notebook editor
- **Persistent WebSocket Connections**: Reliable multi-execution support
- **Token Management System**: User-friendly JupyterHub authentication
- **Real-time Status Monitoring**: Connection and execution status indicators
- **File Browser**: Integrated file management
- **Terminal Access**: Built-in terminal interface

### User Experience
- **BITS Branding**: Custom theme and styling
- **Responsive Design**: Works on desktop and mobile devices
- **Intuitive Interface**: Clean, modern UI with clear navigation
- **Quick Setup**: Guided onboarding for new users
- **Error Handling**: Comprehensive error messages and recovery

### Technical Features
- **TypeScript**: Full type safety and better development experience
- **Vite**: Fast development and build tooling
- **Tailwind CSS**: Utility-first styling framework
- **WebSocket Manager**: Persistent kernel connections
- **Token Validation**: Secure authentication handling

## 📋 Prerequisites

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **JupyterHub** server running on `http://localhost:8888`

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bits-hub-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### JupyterHub Setup

Ensure your JupyterHub server is running and accessible:

```bash
# Start JupyterHub (example)
jupyterhub --config jupyterhub_config.py
```

### Environment Variables

Create a `.env` file in the project root:

```env
# JupyterHub Configuration
VITE_JUPYTER_URL=http://localhost:8888
VITE_API_BASE_URL=/api

# Development Settings
VITE_DEV_MODE=true
```

### Vite Proxy Configuration

The project includes a Vite proxy configuration to handle CORS and WebSocket connections:

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8888',
        changeOrigin: true,
        secure: false,
        ws: true, // Enable WebSocket proxying
      },
      '/jupyter': {
        target: 'http://localhost:8888',
        changeOrigin: true,
        secure: false,
      }
    }
  }
})
```

## 🚀 Quick Start

### 1. First-Time Setup

1. **Start the frontend**
   ```bash
   npm run dev
   ```

2. **Open the application**
   Navigate to `http://localhost:3000`

3. **Configure JupyterHub token**
   - Click "Manage Token" in the header
   - Follow the 3-step setup guide
   - Copy your token from JupyterHub
   - Paste and save the token

4. **Start coding**
   - Create a new notebook
   - Write and execute Python code
   - Enjoy persistent connections!

### 2. Using the Notebook

1. **Create a new cell**
   - Click the "+" button to add code cells
   - Switch between code and markdown modes

2. **Execute code**
   - Click the play button on any cell
   - Use keyboard shortcuts (Shift+Enter)
   - Execute all cells with the "Run All" button

3. **View outputs**
   - Text output appears below cells
   - Plots and images are displayed inline
   - Error messages are clearly shown

## 📁 Project Structure

```
bits-hub-frontend/
├── src/
│   ├── components/           # React components
│   │   ├── TokenManager.tsx  # Token management interface
│   │   ├── TokenStatus.tsx   # Connection status indicator
│   │   ├── QuickSetup.tsx    # Onboarding component
│   │   ├── CustomNotebook.tsx # Main notebook interface
│   │   ├── Header.tsx        # Application header
│   │   └── Notification.tsx  # Toast notifications
│   ├── services/             # API and service layer
│   │   ├── jupyterAPI.ts     # JupyterHub API client
│   │   └── WebSocketManager.ts # WebSocket connection manager
│   ├── pages/                # Page components
│   │   ├── Dashboard.tsx     # Main dashboard
│   │   └── Notebook.tsx      # Notebook page wrapper
│   ├── contexts/             # React contexts
│   │   └── HubContext.tsx    # Application state management
│   └── styles/               # Styling files
│       └── index.css         # Global styles
├── docs/                     # Documentation
│   ├── WEBSOCKET_MANAGER.md  # WebSocket implementation docs
│   └── TOKEN_MANAGEMENT.md   # Token management docs
├── public/                   # Static assets
├── package.json              # Dependencies and scripts
├── vite.config.ts           # Vite configuration
└── README.md                # This file
```

## 🔌 Key Components

### WebSocket Manager

The WebSocket Manager provides persistent connections to Jupyter kernels, solving the multiple execution issue:

```typescript
import { webSocketManager } from './services/WebSocketManager'

// Execute code with persistent connection
const result = await webSocketManager.executeCode(
  kernelId, 
  cellId, 
  'print("Hello, World!")'
)
```

**Features:**
- Persistent connections per kernel
- Message queuing and handling
- Automatic reconnection
- Connection status monitoring

### Token Management System

Comprehensive token management with user-friendly interface:

```typescript
import { JupyterAPI } from './services/jupyterAPI'

// Set token
JupyterAPI.setToken('your-jupyter-token')

// Test token
const isValid = await JupyterAPI.testToken('your-token')

// Get current token
const token = JupyterAPI.getToken()
```

**Features:**
- Multiple token sources (URL, localStorage, HTML)
- Token validation and testing
- Step-by-step setup guide
- Connection diagnostics

### Custom Notebook

Modern notebook interface with enhanced features:

```typescript
// Cell execution
const executeCell = async (cellId: string) => {
  const result = await JupyterAPI.executeCell(kernelId, cellId, code)
  // Handle results and update UI
}
```

**Features:**
- Real-time code execution
- Rich output rendering
- Cell management (add, delete, reorder)
- Execution status tracking

## 🎨 Customization

### BITS Branding

The application uses custom BITS colors and styling:

```css
/* Custom BITS colors */
:root {
  --bits-blue-500: #1e40af;
  --bits-blue-600: #1d4ed8;
  --bits-orange-500: #f97316;
}
```

### Theme Configuration

Modify the theme in `src/index.css`:

```css
@layer components {
  .bits-btn-primary {
    @apply bg-bits-blue-500 hover:bg-bits-blue-600 text-white;
  }
  
  .bits-header {
    @apply bg-bits-blue-500 text-white;
  }
}
```

## 🧪 Testing

### Manual Testing

1. **Connection Testing**
   ```bash
   # Test JupyterHub connectivity
   curl http://localhost:8888/api/status
   ```

2. **Token Testing**
   - Open TokenManager
   - Test token validity
   - Verify connection status

3. **Notebook Testing**
   - Create multiple cells
   - Execute cells sequentially
   - Test rapid execution
   - Verify persistent connections

### Automated Testing

```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run e2e tests
npm run test:e2e
```

## 🐛 Troubleshooting

### Common Issues

#### "No API token available"
**Solution:**
1. Open TokenManager
2. Follow the setup guide
3. Copy token from JupyterHub URL
4. Paste and save the token

#### WebSocket Connection Failed
**Solution:**
1. Check JupyterHub server status
2. Verify token validity
3. Check network connectivity
4. Restart the frontend

#### Multiple Executions Not Working
**Solution:**
1. Ensure WebSocket Manager is working
2. Check connection status indicators
3. Verify kernel is idle before execution
4. Clear browser cache if needed

### Debug Information

Enable debug logging:

```typescript
// In browser console
localStorage.setItem('debug', 'true')

// Check connection status
webSocketManager.getConnectionStatus(kernelId)

// Test token
JupyterAPI.testToken(token)
```

## 📚 API Reference

### JupyterAPI Methods

#### Authentication
- `getToken()`: Get current token
- `setToken(token)`: Set API token
- `clearToken()`: Remove token
- `testToken(token)`: Validate token

#### Kernel Management
- `listKernels()`: Get available kernels
- `createKernel(name)`: Create new kernel
- `getOrCreateKernel(name)`: Get or create kernel
- `executeCell(kernelId, cellId, code)`: Execute code

#### File Operations
- `listFiles(path)`: List directory contents
- `readFile(path)`: Read file content
- `saveNotebook(path, content)`: Save notebook

### WebSocket Manager Methods

- `connectToKernel(kernelId)`: Establish connection
- `executeCode(kernelId, cellId, code)`: Execute code
- `disconnectFromKernel(kernelId)`: Close connection
- `isConnected(kernelId)`: Check connection status
- `getConnectionStatus(kernelId)`: Get detailed status

## 🔄 Development

### Development Workflow

1. **Start development server**
   ```bash
   npm run dev
   ```

2. **Make changes**
   - Edit components in `src/components/`
   - Update services in `src/services/`
   - Modify styles in `src/styles/`

3. **Test changes**
   - Check browser console for errors
   - Test token management
   - Verify notebook functionality

4. **Build for production**
   ```bash
   npm run build
   ```

### Code Style

The project uses:
- **TypeScript** for type safety
- **ESLint** for code linting
- **Prettier** for code formatting
- **Tailwind CSS** for styling

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📦 Deployment

### Production Build

```bash
# Build the application
npm run build

# Preview production build
npm run preview
```

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "run", "preview"]
```

### Environment Configuration

For production deployment, configure:

```env
VITE_JUPYTER_URL=https://your-jupyterhub-server.com
VITE_API_BASE_URL=/api
VITE_DEV_MODE=false
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

For support and questions:

1. **Check the documentation** in the `docs/` folder
2. **Review troubleshooting** section above
3. **Open an issue** on GitHub
4. **Contact the development team**

## 🎯 Roadmap

### Planned Features
- [ ] Multi-kernel support
- [ ] Advanced file browser
- [ ] Collaborative editing
- [ ] Plugin system
- [ ] Mobile optimization

### Technical Improvements
- [ ] Performance monitoring
- [ ] Advanced caching
- [ ] Load balancing
- [ ] Security enhancements

---

**Built with ❤️ for BITS Pilani**
