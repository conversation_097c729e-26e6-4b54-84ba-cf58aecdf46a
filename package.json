{"name": "bits-hub-frontend", "version": "1.0.0", "description": "BITS-branded JupyterHub frontend with custom UI", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000"}, "dependencies": {"axios": "^1.3.0", "child_process": "^1.0.2", "clsx": "^1.2.1", "lucide-react": "^0.263.1", "node-localstorage": "^3.0.5", "node-typescript": "^0.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@types/node": "^24.3.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^4.9.5", "vite": "^4.1.4"}, "keywords": ["jup<PERSON><PERSON><PERSON><PERSON>", "bits", "frontend", "react"], "author": "BITS Pilani", "license": "MIT"}