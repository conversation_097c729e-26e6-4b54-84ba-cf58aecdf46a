# JupyterHub Configuration for BITS Frontend Integration
# Place this file in your JupyterHub installation directory

c = get_config()

# Basic JupyterHub configuration
c.JupyterHub.hub_ip = '0.0.0.0'

# Authentication
c.JupyterHub.authenticator_class = 'jupyterhub.auth.DummyAuthenticator'
c.<PERSON>mmy<PERSON>uthenticator.password = "admin"

# Spawner configuration
c.JupyterHub.spawner_class = 'jupyterhub.spawner.LocalProcessSpawner'

# CORS configuration for frontend integration
c.JupyterHub.allow_credentials = True

# API configuration
c.JupyterHub.api_tokens = {
    'admin-token': 'admin'
}

# Default URL for users
c.Spawner.default_url = '/lab'

# User environment
c.Spawner.environment = {
    'JUPYTER_ENABLE_LAB': 'yes',
    'JUPYTER_TOKEN': '',
    'JUPYTER_CONFIG_DIR': '/home/<USER>/.jupyter'
}

# File system access
c.Spawner.notebook_dir = '/home/<USER>/notebooks'
c.Spawner.working_dir = '/home/<USER>/notebooks'

# Resource limits
c.Spawner.mem_limit = '2G'
c.Spawner.cpu_limit = 1.0

# Timeouts
c.Spawner.start_timeout = 60
c.Spawner.http_timeout = 30

# Logging
c.JupyterHub.log_level = 'INFO'
c.Spawner.debug = True

# Security
c.JupyterHub.trusted_downstream_ips = ['127.0.0.1', '::1']
c.JupyterHub.internal_ssl = False

# Cleanup settings
c.JupyterHub.cleanup_servers = True
c.JupyterHub.cleanup_proxy = True

# Custom settings for BITS branding
c.JupyterHub.template_paths = ['/path/to/bits/templates']
c.JupyterHub.static_paths = ['/path/to/bits/static']

# API rate limiting
c.JupyterHub.rate_limit_window = 60
c.JupyterHub.rate_limit_max_requests = 100

# Session management
c.JupyterHub.cookie_secret_file = '/path/to/cookie_secret'
c.JupyterHub.db_url = 'sqlite:///jupyterhub.sqlite'

# Proxy configuration
c.JupyterHub.proxy_class = 'jupyterhub.proxy.ConfigurableHTTPProxy'
c.ConfigurableHTTPProxy.api_url = 'http://127.0.0.1:8001'

# User management
c.JupyterHub.admin_users = {'admin'}
c.JupyterHub.admin_access = True

# Spawner specific settings
c.LocalProcessSpawner.extra_args = ['--LabApp.default_url=/lab/workspaces/default-bits']

# Environment variables for spawned servers
c.Spawner.env_keep = [
    'PATH',
    'PYTHONPATH',
    'CONDA_ROOT',
    'CONDA_DEFAULT_ENV',
    'VIRTUAL_ENV',
    'LANG',
    'LC_ALL',
    'JUPYTER_ENABLE_LAB'
]

# Custom headers for CORS
c.JupyterHub.headers = {
    'Access-Control-Allow-Origin': 'http://localhost:3000',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
}
