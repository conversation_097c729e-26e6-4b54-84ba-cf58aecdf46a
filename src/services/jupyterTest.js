var spawn = require('child_process').spawn;
// Replace with your actual container name or ID
var containerName = 'wizardly_chandrasekhar';
var docker = spawn('docker', ['exec', containerName, 'jupyter', 'server', 'list']);
var output = '';
var errorOutput = '';
docker.stdout.on('data', function (data) {
    output += data.toString();
});
docker.stderr.on('data', function (data) {
    errorOutput += data.toString();
});
docker.on('close', function (code) {
    if (code !== 0) {
        console.error("Command exited with code ".concat(code));
        if (errorOutput)
            console.error(errorOutput);
        return;
    }
    var tokenMatch = output.match(/token=([a-zA-Z0-9]+)/);
    if (tokenMatch) {
        var token = tokenMatch[1];
        // Insert the token into your frontend programmatically here, or send as a response
        console.log("Jupyter Notebook Token: ".concat(token));
    }
    else {
        console.error('Failed to parse <PERSON><PERSON><PERSON> token from output.');
    }
});
