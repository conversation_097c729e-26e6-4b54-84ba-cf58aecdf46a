
const { spawn } = require('child_process');

// Replace with your actual container name or ID
const containerName = 'wizardly_chandrasekhar';

const docker = spawn('docker', ['exec', containerName, 'jupyter', 'server', 'list']);


let output = '';
let errorOutput = '';

docker.stdout.on('data', (data) => {
  output += data.toString();
});

docker.stderr.on('data', (data) => {
  errorOutput += data.toString();
});

docker.on('close', (code) => {
  if (code !== 0) {
    console.error(`Command exited with code ${code}`);
    if (errorOutput) console.error(errorOutput);
    return;
  }
  const tokenMatch = output.match(/token=([a-zA-Z0-9]+)/);
  if (tokenMatch) {
    const token = tokenMatch[1];
    // Insert the token into your frontend programmatically here, or send as a response
    console.log(`Jupyter Notebook Token: ${token}`);
  } else {
    console.error('Failed to parse <PERSON><PERSON><PERSON> token from output.');
  }
});
