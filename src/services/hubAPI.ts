import axios from 'axios'

const API_BASE = '/api'
const JUPYTER_BASE_URL = '/jupyter' // Use proxy route instead of direct URL

// Configure axios defaults
axios.defaults.withCredentials = true

export class HubAPI {
  static async getCurrentUser() {
    try {
      // For demo purposes, return a mock user
      // In a real setup, this would check JupyterLab authentication
      return {
        name: 'BITS User',
        admin: false,
        server: 'default',
        authenticated: true
      }
    } catch (error) {
      console.log('Authentication check failed:', error)
      return null
    }
  }

  static async login(username: string, password: string) {
    try {
      // For demo purposes, we'll simulate successful login
      // In a real setup, this would validate against JupyterHub
      console.log(`Logging in user: ${username}`)
      return {
        name: username || 'BITS User',
        admin: false,
        server: 'default',
        authenticated: true
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Login failed')
    }
  }

  static async logout() {
    try {
      // Clear any session data
      console.log('Logged out')
      // Redirect to JupyterLab logout if needed
      window.location.href = `${JUPYTER_BASE_URL}/lab`
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  static async getUserServers() {
    try {
      // Return a mock server that points to the actual JupyterLab
      return [
        {
          name: 'default',
          ready: true,
          url: `${JUPYTER_BASE_URL}/lab`,
          last_activity: new Date().toISOString(),
          status: 'running'
        }
      ]
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get servers')
    }
  }

  static async startServer(serverName: string) {
    try {
      // For demo purposes, simulate server start
      console.log(`Starting server: ${serverName}`)
      return { name: serverName, ready: true }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to start server')
    }
  }

  static async stopServer(serverName: string) {
    try {
      // For demo purposes, simulate server stop
      console.log(`Stopping server: ${serverName}`)
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to stop server')
    }
  }

  static async getServerStatus(serverName: string) {
    try {
      // For demo purposes, return mock status
      return { 
        name: serverName, 
        ready: true,
        status: 'running'
      }
    } catch (error) {
      return { name: serverName, ready: false, status: 'stopped' }
    }
  }

  static getServerUrl(serverName: string) {
    return `${JUPYTER_BASE_URL}/lab`
  }

  static getNotebookUrl(serverName: string, path: string) {
    return `${JUPYTER_BASE_URL}/lab/tree/${path}`
  }

  // Method to get JupyterLab URL for embedding
  static getJupyterLabEmbedUrl() {
    const token = this.getJupyterToken()
    return token ? `${JUPYTER_BASE_URL}/lab?token=${token}` : `${JUPYTER_BASE_URL}/lab`
  }

  // Method to get file browser URL for embedding
  static getFileBrowserEmbedUrl() {
    const token = this.getJupyterToken()
    return token ? `${JUPYTER_BASE_URL}/lab/tree?token=${token}` : `${JUPYTER_BASE_URL}/lab/tree`
  }

  // Method to get settings URL for embedding
  static getSettingsEmbedUrl() {
    const token = this.getJupyterToken()
    return token ? `${JUPYTER_BASE_URL}/lab/settings?token=${token}` : `${JUPYTER_BASE_URL}/lab/settings`
  }

  // New method to check if JupyterLab is accessible
  static async checkJupyterLabAccess() {
    try {
      // Try to access the main JupyterLab page
      const response = await axios.get(`${JUPYTER_BASE_URL}/lab`, { 
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept redirects
      })
      return response.status < 400
    } catch (error) {
      console.log('JupyterLab access check failed:', error)
      return false
    }
  }

  // Helper method to get JupyterLab token from URL or localStorage
  static getJupyterToken(): string | null {
    // Try to get token from URL parameters
    const urlParams = new URLSearchParams(window.location.search)
    const token = urlParams.get('token')
    if (token) return token

    // Try to get from localStorage
    const storedToken = localStorage.getItem('jupyter_token')
    if (storedToken) return storedToken

    return null
  }

  // Method to open JupyterLab in a new window/tab (for external access)
  static openJupyterLabExternal() {
    const token = this.getJupyterToken()
    const url = token ? `${JUPYTER_BASE_URL}/lab?token=${token}` : `${JUPYTER_BASE_URL}/lab`
    const newWindow = window.open('', '_blank')
    if (newWindow) {
      newWindow.location.href = url
    }
  }

  // Method to open file browser in a new window/tab (for external access)
  static openFileBrowserExternal() {
    const token = this.getJupyterToken()
    const url = token ? `${JUPYTER_BASE_URL}/lab/tree?token=${token}` : `${JUPYTER_BASE_URL}/lab/tree`
    const newWindow = window.open('', '_blank')
    if (newWindow) {
      newWindow.location.href = url
    }
  }

  // Method to open settings in a new window/tab (for external access)
  static openSettingsExternal() {
    const token = this.getJupyterToken()
    const url = token ? `${JUPYTER_BASE_URL}/lab/settings?token=${token}` : `${JUPYTER_BASE_URL}/lab/settings`
    const newWindow = window.open('', '_blank')
    if (newWindow) {
      newWindow.location.href = url
    }
  }
}
