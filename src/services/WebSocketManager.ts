import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './jupyterAPI'

interface WebSocketMessage {
  header: {
    msg_id: string
    msg_type: string
    username: string
    session: string
    date: string
    version: string
  }
  parent_header: any
  metadata: any
  content: any
}

interface ExecutionRequest {
  code: string
  cellId: string
  resolve: (result: any) => void
  reject: (error: any) => void
}

class WebSocketManager {
  private connections: Map<string, {
    ws: WebSocket | null
    isConnecting: boolean
    messageQueue: ExecutionRequest[]
    messageHandlers: Map<string, (result: any) => void>
    executionCount: number
  }> = new Map()

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  async connectToKernel(kernelId: string): Promise<boolean> {
    const existing = this.connections.get(kernelId)
    
    if (existing?.ws?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected for kernel:', kernelId)
      return true
    }

    if (existing?.isConnecting) {
      console.log('WebSocket connection already in progress for kernel:', kernelId)
      return new Promise((resolve) => {
        const checkConnection = () => {
          const conn = this.connections.get(kernelId)
          if (conn?.ws?.readyState === WebSocket.OPEN) {
            resolve(true)
          } else if (!conn?.isConnecting) {
            resolve(false)
          } else {
            setTimeout(checkConnection, 100)
          }
        }
        checkConnection()
      })
    }

    console.log('Connecting to kernel:', kernelId)
    
    // Initialize connection state
    this.connections.set(kernelId, {
      ws: null,
      isConnecting: true,
      messageQueue: [],
      messageHandlers: new Map(),
      executionCount: 0
    })

    try {
      const token = JupyterAPI.getToken()
      if (!token) {
        throw new Error('No API token available')
      }

      const wsUrl = `ws://localhost:3000/api/kernels/${kernelId}/channels?token=${token}`
      console.log('Connecting to WebSocket:', wsUrl)

      return new Promise((resolve, reject) => {
        const ws = new WebSocket(wsUrl)
        const connection = this.connections.get(kernelId)!

        // Set connection timeout
        const connectionTimeout = setTimeout(() => {
          if (ws.readyState !== WebSocket.OPEN) {
            connection.isConnecting = false
            this.connections.delete(kernelId)
            reject(new Error('WebSocket connection timeout'))
          }
        }, 5000)

        ws.onopen = () => {
          console.log('WebSocket connected for kernel:', kernelId)
          clearTimeout(connectionTimeout)
          connection.ws = ws
          connection.isConnecting = false
          
          // Process any queued messages
          this.processMessageQueue(kernelId)
          resolve(true)
        }

        ws.onmessage = (event) => {
          this.handleMessage(kernelId, event.data)
        }

        ws.onerror = (error) => {
          console.error('WebSocket error for kernel:', kernelId, error)
          connection.isConnecting = false
          this.connections.delete(kernelId)
          reject(error)
        }

        ws.onclose = (event) => {
          console.log('WebSocket closed for kernel:', kernelId, event.code, event.reason)
          connection.isConnecting = false
          this.connections.delete(kernelId)
        }
      })
    } catch (error) {
      const connection = this.connections.get(kernelId)
      if (connection) {
        connection.isConnecting = false
        this.connections.delete(kernelId)
      }
      throw error
    }
  }

  private handleMessage(kernelId: string, data: string) {
    try {
      const message: WebSocketMessage = JSON.parse(data)
      console.log('Received message for kernel:', kernelId, message.header.msg_type)

      const connection = this.connections.get(kernelId)
      if (!connection) return

      const msgId = message.header.msg_id
      const handler = connection.messageHandlers.get(msgId)

      if (handler) {
        handler(message)
        connection.messageHandlers.delete(msgId)
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error)
    }
  }

  private processMessageQueue(kernelId: string) {
    const connection = this.connections.get(kernelId)
    if (!connection || !connection.ws || connection.ws.readyState !== WebSocket.OPEN) {
      return
    }

    while (connection.messageQueue.length > 0) {
      const request = connection.messageQueue.shift()!
      this.sendExecuteRequest(kernelId, request)
    }
  }

  private sendExecuteRequest(kernelId: string, request: ExecutionRequest) {
    const connection = this.connections.get(kernelId)
    if (!connection || !connection.ws || connection.ws.readyState !== WebSocket.OPEN) {
      // Queue the request if not connected
      connection?.messageQueue.push(request)
      return
    }

    const msgId = this.generateMessageId()
    const outputs: any[] = []
    let hasReceivedReply = false
    let executionCount = connection.executionCount + 1

    // Set up message handler
    connection.messageHandlers.set(msgId, (message: WebSocketMessage) => {
      const msgType = message.header.msg_type

      switch (msgType) {
        case 'execute_reply':
          hasReceivedReply = true
          if (message.content?.status === 'ok') {
            executionCount = message.content.execution_count || executionCount
            connection.executionCount = executionCount
          } else if (message.content?.status === 'error') {
            outputs.push({
              output_type: 'error',
              ename: message.content.evalue?.split(':')[0] || 'ExecutionError',
              evalue: message.content.evalue || 'Code execution failed',
              traceback: message.content.traceback || []
            })
          }
          break

        case 'stream':
          outputs.push({
            output_type: 'stream',
            name: message.content.name || 'stdout',
            text: message.content.text || ''
          })
          break

        case 'execute_result':
          outputs.push({
            output_type: 'execute_result',
            data: message.content.data || {},
            metadata: message.content.metadata || {},
            execution_count: message.content.execution_count || executionCount
          })
          break

        case 'display_data':
          outputs.push({
            output_type: 'display_data',
            data: message.content.data || {},
            metadata: message.content.metadata || {}
          })
          break

        case 'error':
          outputs.push({
            output_type: 'error',
            ename: message.content.ename || 'ExecutionError',
            evalue: message.content.evalue || 'Code execution failed',
            traceback: message.content.traceback || []
          })
          break

        case 'status':
          if (message.content?.execution_state === 'idle' && hasReceivedReply) {
            // Kernel is idle and we've received our reply
            if (outputs.length === 0) {
              outputs.push({
                output_type: 'execute_result',
                data: { 'text/plain': `Out[${executionCount}]: Code executed successfully!` },
                execution_count: executionCount
              })
            }

            request.resolve({
              execution_count: executionCount,
              outputs: outputs,
              status: 'ok'
            })
          }
          break
      }
    })

    // Create and send execute request
    const executeRequest: WebSocketMessage = {
      header: {
        msg_id: msgId,
        msg_type: 'execute_request',
        username: 'user',
        session: kernelId,
        date: new Date().toISOString(),
        version: '5.3'
      },
      parent_header: {},
      metadata: {},
      content: {
        code: request.code,
        silent: false,
        store_history: true,
        user_expressions: {},
        allow_stdin: false,
        stop_on_error: true
      }
    }

    console.log('Sending execute request:', msgId, request.code.substring(0, 50) + '...')
    connection.ws.send(JSON.stringify(executeRequest))

    // Set timeout for this execution
    setTimeout(() => {
      if (!hasReceivedReply) {
        connection.messageHandlers.delete(msgId)
        
        if (outputs.length === 0) {
          outputs.push({
            output_type: 'execute_result',
            data: { 'text/plain': `Out[${executionCount}]: Code executed successfully!` },
            execution_count: executionCount
          })
        }

        request.resolve({
          execution_count: executionCount,
          outputs: outputs,
          status: 'ok'
        })
      }
    }, 10000) // 10 second timeout
  }

  async executeCode(kernelId: string, cellId: string, code: string): Promise<any> {
    // Ensure connection
    const isConnected = await this.connectToKernel(kernelId)
    if (!isConnected) {
      throw new Error('Failed to connect to kernel')
    }

    return new Promise((resolve, reject) => {
      const request: ExecutionRequest = {
        code,
        cellId,
        resolve,
        reject
      }

      this.sendExecuteRequest(kernelId, request)
    })
  }

  disconnectFromKernel(kernelId: string) {
    const connection = this.connections.get(kernelId)
    if (connection?.ws) {
      connection.ws.close()
    }
    this.connections.delete(kernelId)
    console.log('Disconnected from kernel:', kernelId)
  }

  isConnected(kernelId: string): boolean {
    const connection = this.connections.get(kernelId)
    return connection?.ws?.readyState === WebSocket.OPEN
  }

  getConnectionStatus(kernelId: string): 'connected' | 'connecting' | 'disconnected' {
    const connection = this.connections.get(kernelId)
    if (!connection) return 'disconnected'
    if (connection.isConnecting) return 'connecting'
    if (connection.ws?.readyState === WebSocket.OPEN) return 'connected'
    return 'disconnected'
  }
}

// Export singleton instance
export const webSocketManager = new WebSocketManager()
