import axios from 'axios'
import { webSocketManager } from './WebSocketManager'

import { JupyterTokenService } from './JupyterTokenService'

const JUPYTER_BASE_URL = '/jupyter' // Using our proxy

// Configure axios defaults
axios.defaults.withCredentials = true

// Create axios instance with interceptors
const api = axios.create({
  baseURL: '', // Use relative URLs to work with our proxy
  withCredentials: true,
  timeout: 10000
})

// Request interceptor to add CSRF token and API token
api.interceptors.request.use(async (config) => {
  // For JupyterLab, we need to get the XSRF token from cookies
  if (!config.headers['X-XSRFToken']) {
    try {
      // Get XSRF token from cookies
      const cookies = document.cookie.split(';')
      const xsrfCookie = cookies.find(cookie => cookie.trim().startsWith('_xsrf='))
      if (xsrfCookie) {
        const xsrfToken = xsrfCookie.split('=')[1]
        config.headers['X-XSRFToken'] = decodeURIComponent(xsrfToken)
      }
    } catch (error) {
      console.warn('Could not get XSRF token from cookies:', error)
    }
  }
  
  // Add API token to URL parameters
  const token = JupyterAPI.getToken()
  if (token && config.url && !config.url.includes('token=')) {
    const separator = config.url.includes('?') ? '&' : '?'
    config.url = `${config.url}${separator}token=${token}`
  }
  
  return config
})

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export interface NotebookCell {
  id: string
  cell_type: 'code' | 'markdown'
  source: string[]
  metadata: any
  execution_count?: number
  outputs?: any[]
}

export interface NotebookContent {
  cells: NotebookCell[]
  metadata: any
  nbformat: number
  nbformat_minor: number
}

export interface FileItem {
  name: string
  path: string
  type: 'file' | 'directory'
  size?: number
  last_modified?: string
  content?: string
  mimetype?: string
}

export interface KernelInfo {
  id: string
  name: string
  last_activity: string
  execution_state: 'idle' | 'busy' | 'starting'
  connections: number
}

export class JupyterAPI {
  // File Operations
  static async listFiles(path: string = ''): Promise<FileItem[]> {
    try {
      const response = await api.get(`/api/contents${path}`)
      return response.data.content || []
    } catch (error) {
      console.error('Error listing files:', error)
      // Return mock data for development
      return [
        {
          name: 'Untitled.ipynb',
          path: '/Untitled.ipynb',
          type: 'file',
          size: 1024,
          last_modified: new Date().toISOString(),
          mimetype: 'application/x-ipynb+json'
        },
        {
          name: 'data',
          path: '/data',
          type: 'directory',
          last_modified: new Date().toISOString()
        }
      ]
    }
  }

  static async getFile(path: string): Promise<FileItem> {
    try {
      const response = await api.get(`/api/contents${path}`)
      return response.data
    } catch (error) {
      console.error('Error getting file:', error)
      throw error
    }
  }

  static async createFile(path: string, type: 'file' | 'directory'): Promise<FileItem> {
    try {
      const response = await api.post(`/api/contents${path}`, {
        type,
        ext: type === 'file' ? '.ipynb' : undefined
      })
      return response.data
    } catch (error) {
      console.error('Error creating file:', error)
      throw error
    }
  }

  static async deleteFile(path: string): Promise<void> {
    try {
      await api.delete(`/api/contents${path}`)
    } catch (error) {
      console.error('Error deleting file:', error)
      throw error
    }
  }

  static async uploadFile(path: string, file: File): Promise<FileItem> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await api.post(`/api/contents${path}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('Error uploading file:', error)
      throw error
    }
  }

  // Notebook Operations
  static async getNotebook(path: string): Promise<NotebookContent> {
    try {
      const response = await api.get(`/api/contents${path}`)
      return response.data.content
    } catch (error) {
      console.error('Error getting notebook:', error)
      // Return mock notebook for development
      return {
        cells: [
          {
            id: 'cell_1',
            cell_type: 'code',
            source: ['# Welcome to BITS Data Science Platform\nprint("Hello, World!")'],
            metadata: {},
            execution_count: undefined,
            outputs: []
          }
        ],
        metadata: {
          kernelspec: {
            display_name: 'Python 3',
            language: 'python',
            name: 'python3'
          }
        },
        nbformat: 4,
        nbformat_minor: 4
      }
    }
  }

  static async saveNotebook(path: string, content: NotebookContent): Promise<void> {
    try {
      await api.put(`/api/contents${path}`, {
        type: 'notebook',
        content
      })
    } catch (error) {
      console.error('Error saving notebook:', error)
      throw error
    }
  }

  // Kernel Operations
  static async listKernels(): Promise<KernelInfo[]> {
    try {
      const response = await api.get(`/api/kernels`)
      return response.data
    } catch (error) {
      console.error('Error listing kernels:', error)
      // Return mock kernel for development
      return [
        {
          id: 'kernel_1',
          name: 'python3',
          last_activity: new Date().toISOString(),
          execution_state: 'idle',
          connections: 1
        }
      ]
    }
  }

  static async startKernel(): Promise<KernelInfo> {
    try {
      const response = await api.post(`/api/kernels`, {
        name: 'python3'
      })
      return response.data
    } catch (error) {
      console.error('Error starting kernel:', error)
      throw error
    }
  }

  static async executeCell(kernelId: string, cellId: string, code: string): Promise<any> {
    try {
      // Ensure we have XSRF token and API token
      await this.ensureXSRFToken()
      
      console.log('Executing code via WebSocket Manager:', code)
      
      // Get the API token
      let token = this.getToken()
      if (!token) {
        // Try to extract token from current page
        console.log('No stored token, trying to extract from current page...')
        await this.extractTokenFromPage()
        token = this.getToken()
      }
      
      if (!token) {
        console.error('No API token available for WebSocket connection')
        console.log('To fix this:')
        console.log('1. Open http://localhost:8888 in your browser')
        console.log('2. Copy the token from the URL (?token=...)')
        console.log('3. Use JupyterAPI.setToken(token) in the browser console')
        console.log('4. Or add ?token=YOUR_TOKEN to your frontend URL')
        console.log('Falling back to mock execution for now...')
        
        const mockOutputs = this.generateMockOutputs(code)
        return {
          execution_count: 1,
          outputs: mockOutputs,
          status: 'ok'
        }
      }
      
      // Use the WebSocket manager for persistent connections
      return await webSocketManager.executeCode(kernelId, cellId, code)
      
    } catch (error: any) {
      console.error('Error executing cell:', error)
      const mockOutputs = this.generateMockOutputs(code)
      return {
        execution_count: 1,
        outputs: mockOutputs,
        status: 'error'
      }
    }
  }

  static async interruptKernel(kernelId: string): Promise<void> {
    try {
      await api.post(`/api/kernels/${kernelId}/interrupt`)
    } catch (error) {
      console.error('Error interrupting kernel:', error)
      throw error
    }
  }

  static async restartKernel(kernelId: string): Promise<void> {
    try {
      await api.post(`/api/kernels/${kernelId}/restart`)
    } catch (error) {
      console.error('Error restarting kernel:', error)
      throw error
    }
  }

  // Session Operations
  static async listSessions(): Promise<any[]> {
    try {
      const response = await api.get(`/api/sessions`)
      return response.data
    } catch (error) {
      console.error('Error listing sessions:', error)
      return []
    }
  }

  static async createSession(path: string, kernelName: string = 'python3'): Promise<any> {
    try {
      const response = await api.post(`/api/sessions`, {
        path,
        type: 'notebook',
        kernel: {
          name: kernelName
        }
      })
      return response.data
    } catch (error) {
      console.error('Error creating session:', error)
      throw error
    }
  }

  static async getOrCreateKernel(kernelName: string = 'python3'): Promise<string> {
    try {
      // First, try to get an existing kernel
      const kernels = await this.listKernels()
      const existingKernel = kernels.find(k => k.name === kernelName && k.execution_state === 'idle')
      
      if (existingKernel) {
        console.log('Using existing kernel:', existingKernel.id)
        return existingKernel.id
      }
      
      // If no idle kernel exists, create a new one
      console.log('Creating new kernel:', kernelName)
      const newKernel = await this.createKernel(kernelName)
      return newKernel.id
    } catch (error) {
      console.error('Error getting or creating kernel:', error)
      throw error
    }
  }

  static async createKernel(kernelName: string = 'python3'): Promise<any> {
    try {
      const response = await api.post(`/api/kernels`, {
        name: kernelName
      })
      return response.data
    } catch (error) {
      console.error('Error creating kernel:', error)
      throw error
    }
  }

  // Utility Methods
  static async checkConnection(): Promise<boolean> {
    try {
      await api.get(`/api/status`)
      return true
    } catch (error) {
      console.error('JupyterLab connection check failed:', error)
      return false
    }
  }

  static async ensureXSRFToken(): Promise<boolean> {
    try {
      // Check if we already have an XSRF token
      const cookies = document.cookie.split(';')
      const xsrfCookie = cookies.find(cookie => cookie.trim().startsWith('_xsrf='))
      
      if (!xsrfCookie) {
        // Visit JupyterLab to get the XSRF token and API token
        const response = await axios.get(`/jupyter/lab`, {
          withCredentials: true
        })
        
        // Extract API token from the response
        const tokenMatch = response.data.match(/data-jupyter-api-token="([^"]+)"/)
        if (tokenMatch) {
          const apiToken = tokenMatch[1]
          localStorage.setItem('jupyter_token', apiToken)
          console.log('Stored API token:', apiToken)
        }
        
        // Check if we got the XSRF token
        const newCookies = document.cookie.split(';')
        const newXsrfCookie = newCookies.find(cookie => cookie.trim().startsWith('_xsrf='))
        return !!newXsrfCookie
      }
      
      return true
    } catch (error) {
      console.error('Error ensuring XSRF token:', error)
      return false
    }
  }

  static async extractTokenFromPage(): Promise<void> {
    try {
      // Try to get token from current page body attribute
      const bodyToken = document.body?.getAttribute('data-jupyter-api-token')
      if (bodyToken) {
        localStorage.setItem('jupyter_token', bodyToken)
        console.log('Extracted token from page body:', bodyToken)
        return
      }
      
      // Try to get token from URL parameters first
      const urlParams = new URLSearchParams(window.location.search)
      const urlToken = urlParams.get('token')
      if (urlToken) {
        localStorage.setItem('jupyter_token', urlToken)
        console.log('Extracted token from URL:', urlToken)
        return
      }
      
      // If not found, try to visit JupyterLab and extract
      console.log('Token not found on current page, visiting JupyterLab...')
      const response = await axios.get(`/jupyter/lab`, {
        withCredentials: true
      })
      
      // Try multiple patterns to extract the token
      const patterns = [
        /data-jupyter-api-token="([^"]+)"/,
        /data-jupyter-api-token='([^']+)'/,
        /"api_token":"([^"]+)"/,
        /'api_token':'([^']+)'/,
        /token=([a-f0-9]+)/i
      ]
      
      for (const pattern of patterns) {
        const match = response.data.match(pattern)
        if (match) {
          const apiToken = match[1]
          localStorage.setItem('jupyter_token', apiToken)
          console.log('Extracted token using pattern:', pattern, 'Token:', apiToken)
          return
        }
      }
      
      // If still not found, try to generate a token using the API
      console.log('No token found in HTML, trying to generate one...')
      try {
        const tokenResponse = await axios.post(`/api/security/token`, {}, {
          withCredentials: true
        })
        if (tokenResponse.data && tokenResponse.data.token) {
          localStorage.setItem('jupyter_token', tokenResponse.data.token)
          console.log('Generated new token:', tokenResponse.data.token)
          return
        }
      } catch (tokenError) {
        console.warn('Could not generate token:', tokenError)
      }
      
      console.warn('Could not find or generate API token')
    } catch (error) {
      console.error('Error extracting token from page:', error)
    }
  }

  static getToken(): string | null {
    // Try to get token from URL parameters first
    const urlParams = new URLSearchParams(window.location.search)
    const token = urlParams.get('token')
    if (token) {
      console.log('Got token from URL parameters:', token)
      return token
    }

    // Try to get from localStorage
    const storedToken = localStorage.getItem('jupyter_token')
    if (storedToken) {
      console.log('Got token from localStorage:', storedToken)
      return storedToken
    }

    // Try to extract from the HTML response (fallback)
    try {
      const match = document.body?.getAttribute('data-jupyter-api-token')
      if (match) {
        console.log('Got token from HTML body attribute:', match)
        return match
      }
    } catch (error) {
      console.warn('Could not extract token from HTML:', error)
    }

    console.log('No token found in any source')
    return null
  }

  static setToken(token: string): void {
    localStorage.setItem('jupyter_token', token)
    console.log('Token manually set:', token)
  }

  static clearToken(): void {
    localStorage.removeItem('jupyter_token')
    console.log('Token cleared from localStorage')
  }

  static async testToken(token: string): Promise<boolean> {
    try {
      const response = await axios.get(`/api/status?token=${token}`)
      console.log('Token test successful:', response.data)
      return true
    } catch (error) {
      console.error('Token test failed:', error)
      return false
    }
  }

  static getTokenFromJupyterLab(): string | null {
    // This method helps users get the token from JupyterLab
    // Users should:
    // 1. Open JupyterLab in their browser (http://localhost:8888)
    // 2. Look at the URL - it should contain ?token=...
    // 3. Copy that token and use JupyterAPI.setToken(token)
    
    console.log('To get your JupyterLab API token:')
    console.log('1. Open http://localhost:8888 in your browser')
    console.log('2. Look at the URL - it should contain ?token=...')
    console.log('3. Copy that token and use JupyterAPI.setToken(token)')
    console.log('4. Or add ?token=YOUR_TOKEN to your frontend URL')
    
    return null
  }

  static async diagnoseConnection(): Promise<void> {
    console.log('=== JupyterLab Connection Diagnosis ===')
    
    // Check if we have a token
    const token = this.getToken()
    if (token) {
      console.log('✅ Token found:', token.substring(0, 10) + '...')
      
      // Test the token
      const isValid = await this.testToken(token)
      if (isValid) {
        console.log('✅ Token is valid and working')
      } else {
        console.log('❌ Token is invalid or expired')
      }
    } else {
      console.log('❌ No token found')
      this.getTokenFromJupyterLab()
    }
    
    // Check connection
    const isConnected = await this.checkConnection()
    if (isConnected) {
      console.log('✅ JupyterLab server is reachable')
    } else {
      console.log('❌ Cannot reach JupyterLab server')
    }
    
    console.log('=== End Diagnosis ===')
  }

  static async getCSRFToken(): Promise<string | null> {
    try {
      // Get XSRF token from cookies
      const cookies = document.cookie.split(';')
      const xsrfCookie = cookies.find(cookie => cookie.trim().startsWith('_xsrf='))
      if (xsrfCookie) {
        const xsrfToken = xsrfCookie.split('=')[1]
        return decodeURIComponent(xsrfToken)
      }
      return null
    } catch (error) {
      console.error('Error getting XSRF token from cookies:', error)
      return null
    }
  }

  static generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  static generateMockOutputs(code: string): any[] {
    const outputs: any[] = []
    
    if (code.includes('print(')) {
      // Extract print statements
      const printMatch = code.match(/print\(['"]([^'"]+)['"]\)/)
      if (printMatch) {
        outputs.push({
          output_type: 'stream',
          name: 'stdout',
          text: printMatch[1] + '\n'
        })
      }
    }
    
    if (code.includes('import numpy')) {
      outputs.push({
        output_type: 'stream',
        name: 'stdout',
        text: 'NumPy imported successfully\n'
      })
    }
    
    if (code.includes('arr.sum()')) {
      outputs.push({
        output_type: 'stream',
        name: 'stdout',
        text: 'Array: [1 2 3 4 5]\nSum: 15\n'
      })
    }
    
    // If no specific outputs, create a generic success message
    if (outputs.length === 0) {
      outputs.push({
        output_type: 'execute_result',
        data: { 'text/plain': `Out[1]: Code executed successfully!` },
        execution_count: 1
      })
    }
    
    return outputs
  }

  /**
   * Automatically fetch and set the Jupyter API token for the current user.
   * This works if the JupyterHub API allows token creation via POST /hub/api/users/:username/tokens.
   * Requires the user to be authenticated (e.g., via DummyAuthenticator).
   */
  static async autoLoginAndSetToken(username: string, password: string): Promise<boolean> {
    try {
      // 1. Log in to JupyterHub (if not already authenticated)
      // This step may vary depending on your JupyterHub authenticator.
      // For DummyAuthenticator, you can POST to /hub/login
      await axios.post('/jupyter/hub/login', new URLSearchParams({
        username,
        password
      }), {
        withCredentials: true,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      });

      // 2. Request a new API token for the user
      const tokenResp = await axios.post(
        `/jupyter/hub/api/users/${encodeURIComponent(username)}/tokens`,
        { note: 'Auto-generated by frontend' },
        {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        }
      );

      if (tokenResp.data && tokenResp.data.token) {
        this.setToken(tokenResp.data.token);
        console.log('✅ Jupyter API token set automatically:', tokenResp.data.token);
        return true;
      } else {
        console.error('❌ Failed to retrieve token from JupyterHub API');
        return false;
      }
    } catch (error) {
      console.error('❌ Auto-login and token retrieval failed:', error);
      return false;
    }
  }
}

// Set token from URL for development convenience
JupyterTokenService.setTokenFromUrl('http://localhost:8888/?token=abc123');
