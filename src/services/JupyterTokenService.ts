import axios from 'axios';


export class JupyterTokenService {
  /**
   * Extract token from a Jupyter server URL (e.g., /?token=abc123) and store it in localStorage.
   */
  static setTokenFromUrl(url: string): boolean {
    try {
      const parsed = new URL(url);
      const token = parsed.searchParams.get("token");
      if (token) {
        localStorage.setItem('jupyter_token', token);
        console.log("✅ Jupyter token set from URL:", token);
        return true;
      } else {
        console.error("❌ No token found in URL");
        return false;
      }
    } catch (err) {
      console.error("❌ Failed to parse token from URL:", err);
      return false;
    }
  }

  /**
   * Get the currently stored token from localStorage.
   */
  static getToken(): string | null {
    return localStorage.getItem('jupyter_token');
  }

  /**
   * Example: Call Jupyter REST API using stored token.
   * Uses the same token-in-query style as your codebase.
   */
  static async getKernelSpecs(baseUrl: string) {
    const token = this.getToken();
    if (!token) throw new Error("Token not set");
    const resp = await axios.get(`${baseUrl}/api/kernelspecs?token=${token}`);
    return resp.data;
  }
}


JupyterTokenService.setTokenFromUrl('http://localhost:8888/?token=abc123');

const token = JupyterTokenService.getToken();