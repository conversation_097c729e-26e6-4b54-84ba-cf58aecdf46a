import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useHub } from '../contexts/HubContext'
import { HubAPI } from '../services/hubAPI'
import { JupyterAPI } from '../services/jupyterAPI'
import { 
  Play, 
  Plus, 
  BookOpen, 
  Database, 
  Code, 
  Upload,
  Download,
  Settings
} from 'lucide-react'
import QuickSetup from '../components/QuickSetup'
import TokenManager from '../components/TokenManager'

const Dashboard: React.FC = () => {
  const { user, servers, startServer, stopServer } = useHub()
  const navigate = useNavigate()
  const [hasToken, setHasToken] = useState(false)
  const [showTokenManager, setShowTokenManager] = useState(false)

  useEffect(() => {
    checkTokenStatus()
  }, [])

  const checkTokenStatus = () => {
    const token = JupyterAPI.getToken()
    setHasToken(!!token)
  }

  const handleTokenSet = (token: string) => {
    setHasToken(!!token)
  }

  const quickActions = [
    {
      title: 'New Notebook',
      description: 'Create a new Jupyter notebook',
      icon: BookOpen,
      color: 'bg-bits-blue-500',
      action: () => {
        // Navigate to embedded notebook view
        navigate('/notebook/default')
      }
    },
    {
      title: 'Upload Dataset',
      description: 'Upload CSV or other data files',
      icon: Upload,
      color: 'bg-bits-orange-500',
      action: () => {
        // Navigate to embedded notebook view
        navigate('/notebook/default')
      }
    },
    {
      title: 'New Script',
      description: 'Create a Python script',
      icon: Code,
      color: 'bg-green-500',
      action: () => {
        // Navigate to embedded notebook view
        navigate('/notebook/default')
      }
    },
    {
      title: 'Settings',
      description: 'Configure your workspace',
      icon: Settings,
      color: 'bg-gray-500',
      action: () => {
        // Navigate to embedded notebook view
        navigate('/notebook/default')
      }
    }
  ]

  const handleServerAction = async (serverName: string, action: 'start' | 'stop') => {
    if (action === 'start') {
      await startServer(serverName)
    } else {
      await stopServer(serverName)
    }
  }

  const handleOpenServer = (server: any) => {
    // Navigate to embedded notebook view
    navigate(`/notebook/${server.name}`)
  }

  return (
    <div className="space-y-6">
      {/* Token Setup Section */}
      {!hasToken && (
        <QuickSetup onOpenTokenManager={() => setShowTokenManager(true)} />
      )}

      {/* Welcome Section */}
      <div className="bits-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back, {user?.name || 'User'}!
            </h1>
            <p className="text-gray-600">
              Ready to explore data science? Start with a new notebook or continue your work.
            </p>
          </div>
          <div className="w-12 h-12 bg-bits-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-lg">B</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="bits-card hover:shadow-md transition-shadow text-left group"
            >
              <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center mb-3 group-hover:scale-105 transition-transform`}>
                <action.icon size={24} className="text-white" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">{action.title}</h3>
              <p className="text-sm text-gray-600">{action.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Server Status */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Your Servers</h2>
        {servers.length === 0 ? (
          <div className="bits-card text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Play size={24} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No servers running</h3>
            <p className="text-gray-600 mb-4">Start a server to begin working with notebooks</p>
            <button 
              className="bits-btn-primary"
              onClick={() => startServer('default')}
            >
              <Plus size={16} className="mr-2" />
              Start New Server
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {servers.map((server) => (
              <div key={server.name} className="bits-card">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{server.name}</h3>
                  <div className={`w-3 h-3 rounded-full ${server.ready ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Status: {server.ready ? 'Running' : 'Starting...'}
                </p>
                <div className="flex space-x-2">
                  {server.ready ? (
                    <>
                      <button 
                        className="bits-btn-primary flex-1"
                        onClick={() => handleOpenServer(server)}
                      >
                        <BookOpen size={16} className="mr-2" />
                        Open
                      </button>
                      <button
                        onClick={() => handleServerAction(server.name, 'stop')}
                        className="bits-btn-secondary"
                      >
                        Stop
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => handleServerAction(server.name, 'start')}
                      className="bits-btn-primary w-full"
                    >
                      <Play size={16} className="mr-2" />
                      Start
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Recent Activity */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="bits-card">
          <div className="text-center py-8 text-gray-500">
            <Database size={48} className="mx-auto mb-4 text-gray-300" />
            <p>No recent activity</p>
            <p className="text-sm">Your recent notebooks and files will appear here</p>
            <button 
              className="bits-btn-primary mt-4"
              onClick={() => navigate('/notebook/default')}
            >
              <BookOpen size={16} className="mr-2" />
              Open Workspace
            </button>
          </div>
        </div>
      </div>

      {/* Token Manager Modal */}
      <TokenManager
        isOpen={showTokenManager}
        onClose={() => setShowTokenManager(false)}
        onTokenSet={handleTokenSet}
      />
    </div>
  )
}

export default Dashboard
