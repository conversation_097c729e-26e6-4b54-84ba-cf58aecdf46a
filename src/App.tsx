import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import { HubProvider } from './contexts/HubContext'
import Header from './components/Header'
import Sidebar from './components/Sidebar'
import Dashboard from './pages/Dashboard'
import Login from './pages/Login'
import Notebook from './pages/Notebook'
import './App.css'

function App() {
  return (
    <HubProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={
              <div className="flex">
                <Sidebar />
                <div className="flex-1">
                  <Header />
                  <main className="p-6">
                    <Dashboard />
                  </main>
                </div>
              </div>
            } />
            <Route path="/notebook/:id" element={<Notebook />} />
          </Routes>
        </div>
      </Router>
    </HubProvider>
  )
}

export default App
