import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { HubAPI } from '../services/hubAPI'

interface HubContextType {
  user: any
  servers: any[]
  loading: boolean
  error: string | null
  login: (username: string, password: string) => Promise<boolean>
  logout: () => void
  startServer: (serverName: string) => Promise<boolean>
  stopServer: (serverName: string) => Promise<boolean>
  refreshServers: () => Promise<void>
}

const HubContext = createContext<HubContextType | undefined>(undefined)

export const useHub = () => {
  const context = useContext(HubContext)
  if (!context) {
    throw new Error('useHub must be used within a HubProvider')
  }
  return context
}

interface HubProviderProps {
  children: ReactNode
}

export const HubProvider: React.FC<HubProviderProps> = ({ children }) => {
  const [user, setUser] = useState<any>(null)
  const [servers, setServers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const userData = await HubAPI.getCurrentUser()
      setUser(userData)
      if (userData) {
        await refreshServers()
      }
    } catch (err) {
      console.log('Not authenticated')
    } finally {
      setLoading(false)
    }
  }

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)
      const userData = await HubAPI.login(username, password)
      setUser(userData)
      await refreshServers()
      return true
    } catch (err: any) {
      setError(err.message || 'Login failed')
      return false
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await HubAPI.logout()
      setUser(null)
      setServers([])
    } catch (err) {
      console.error('Logout error:', err)
    }
  }

  const startServer = async (serverName: string): Promise<boolean> => {
    try {
      await HubAPI.startServer(serverName)
      await refreshServers()
      return true
    } catch (err: any) {
      setError(err.message || 'Failed to start server')
      return false
    }
  }

  const stopServer = async (serverName: string): Promise<boolean> => {
    try {
      await HubAPI.stopServer(serverName)
      await refreshServers()
      return true
    } catch (err: any) {
      setError(err.message || 'Failed to stop server')
      return false
    }
  }

  const refreshServers = async () => {
    if (!user) return
    try {
      const serversData = await HubAPI.getUserServers()
      setServers(serversData)
    } catch (err) {
      console.error('Failed to refresh servers:', err)
    }
  }

  const value: HubContextType = {
    user,
    servers,
    loading,
    error,
    login,
    logout,
    startServer,
    stopServer,
    refreshServers
  }

  return (
    <HubContext.Provider value={value}>
      {children}
    </HubContext.Provider>
  )
}
