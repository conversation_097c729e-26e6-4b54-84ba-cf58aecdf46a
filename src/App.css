/* Additional custom styles */
.notebook-iframe {
  width: 100%;
  height: calc(100vh - 120px);
  border: none;
  border-radius: 8px;
}

.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0052CC;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
