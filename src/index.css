@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .bits-btn-primary {
    @apply bg-bits-blue-500 hover:bg-bits-blue-600 text-white font-medium py-2 px-4 rounded-bits transition-colors duration-200;
  }
  
  .bits-btn-secondary {
    @apply bg-white hover:bg-gray-50 text-bits-blue-500 border border-bits-blue-500 font-medium py-2 px-4 rounded-bits transition-colors duration-200;
  }
  
  .bits-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .bits-sidebar {
    @apply bg-white border-r border-gray-200 w-64 min-h-screen;
  }
  
  .bits-header {
    @apply bg-bits-blue-500 text-white shadow-sm;
  }
}

/* Notification animations */
@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}
