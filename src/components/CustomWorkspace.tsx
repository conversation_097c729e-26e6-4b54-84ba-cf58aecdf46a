import React, { useState } from 'react'
import { <PERSON><PERSON>ef<PERSON>, Folder, FileText, Settings, Grid, Play, Database, Code, Terminal } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import CustomNotebook from './CustomNotebook'
import CustomFileBrowser from './CustomFileBrowser'

type ViewType = 'notebook' | 'filebrowser' | 'terminal' | 'settings'

const CustomWorkspace: React.FC = () => {
  const navigate = useNavigate()
  const [activeView, setActiveView] = useState<ViewType>('notebook')
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const handleBack = () => {
    navigate('/')
  }

  const renderActiveView = () => {
    switch (activeView) {
      case 'notebook':
        return <CustomNotebook />
      case 'filebrowser':
        return <CustomFileBrowser />
      case 'terminal':
        return (
          <div className="h-full flex flex-col bg-black text-green-400 p-4">
            <div className="flex-1 font-mono text-sm">
              <div className="mb-2">BITS Data Science Terminal</div>
              <div className="mb-2">Python 3.8.10 (default, May 4 2021, 00:00:00)</div>
              <div className="mb-2">[GCC 9.4.0] on linux</div>
              <div className="mb-2">Type "help", "copyright", "credits" or "license" for more information.</div>
                              <div className="flex items-center">
                  <span className="mr-2">{'>>> '}</span>
                  <input 
                    type="text" 
                    className="flex-1 bg-transparent border-none outline-none"
                    placeholder="Enter Python code..."
                  />
                </div>
            </div>
          </div>
        )
      case 'settings':
        return (
          <div className="h-full p-6">
            <h2 className="text-xl font-semibold mb-4">Settings</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Theme
                </label>
                <select className="w-full border border-gray-300 rounded-md px-3 py-2">
                  <option>BITS Light</option>
                  <option>BITS Dark</option>
                  <option>System Default</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Font Size
                </label>
                <input 
                  type="range" 
                  min="10" 
                  max="20" 
                  defaultValue="14"
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Auto Save
                </label>
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-gray-600">Save notebooks automatically</span>
              </div>
            </div>
          </div>
        )
      default:
        return <CustomNotebook />
    }
  }

  const sidebarItems = [
    {
      id: 'filebrowser' as ViewType,
      icon: Folder,
      label: 'File Browser',
      description: 'Browse and manage files'
    },
    {
      id: 'notebook' as ViewType,
      icon: FileText,
      label: 'Notebook',
      description: 'Create and edit notebooks'
    },
    {
      id: 'terminal' as ViewType,
      icon: Terminal,
      label: 'Terminal',
      description: 'Command line interface'
    },
    {
      id: 'settings' as ViewType,
      icon: Settings,
      label: 'Settings',
      description: 'Configure workspace'
    }
  ]

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Top Bar */}
      <div className="border-b border-gray-200 bg-white px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft size={16} />
            <span>Back to Dashboard</span>
          </button>
          <div className="h-4 w-px bg-gray-300"></div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">BITS Data Science Workspace</h1>
            <p className="text-sm text-gray-600">Custom JupyterLab Interface</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-sm text-gray-600">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span>Python 3 (ipykernel)</span>
          </div>
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="p-2 hover:bg-gray-100 rounded"
          >
            <Grid size={16} />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar */}
        {!sidebarCollapsed && (
          <div className="w-64 border-r border-gray-200 bg-gray-50 flex flex-col">
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-sm font-medium text-gray-900">Workspace</h3>
            </div>

            {/* Sidebar Items */}
            <div className="flex-1 overflow-y-auto">
              {sidebarItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveView(item.id)}
                    className={`w-full p-3 text-left hover:bg-gray-100 transition-colors ${
                      activeView === item.id ? 'bg-blue-50 border-r-2 border-bits-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon size={16} className={activeView === item.id ? 'text-bits-blue-500' : 'text-gray-500'} />
                      <div className="flex-1 min-w-0">
                        <p className={`text-sm font-medium ${
                          activeView === item.id ? 'text-bits-blue-500' : 'text-gray-900'
                        }`}>
                          {item.label}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </button>
                )
              })}
            </div>

            {/* Sidebar Footer */}
            <div className="p-4 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                <div>BITS Pilani</div>
                <div>Data Science Platform</div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* View Tabs */}
          <div className="border-b border-gray-200 bg-white">
            <div className="flex items-center space-x-1 px-4 py-2">
              {sidebarItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveView(item.id)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-t-md text-sm font-medium transition-colors ${
                      activeView === item.id
                        ? 'bg-white text-bits-blue-500 border-b-2 border-bits-blue-500'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon size={14} />
                    <span>{item.label}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* View Content */}
          <div className="flex-1 overflow-hidden">
            {renderActiveView()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CustomWorkspace
