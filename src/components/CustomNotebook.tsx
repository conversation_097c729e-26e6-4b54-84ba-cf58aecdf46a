import React, { useState, useEffect, useRef } from 'react'
import { Play, Square, RotateCcw, FastForward, Code, FileText, Save, Plus, Trash2, Settings } from 'lucide-react'
import { JupyterAPI } from '../services/jupyterAPI'
import { webSocketManager } from '../services/WebSocketManager'

interface Cell {
  id: string
  type: 'code' | 'markdown'
  content: string
  output?: any[]
  execution_count?: number
  metadata: any
}

interface Notebook {
  name: string
  path: string
  content: {
    cells: Cell[]
    metadata: any
    nbformat: number
    nbformat_minor: number
  }
}

const CustomNotebook: React.FC = () => {
  const [notebook, setNotebook] = useState<Notebook | null>(null)
  const [cells, setCells] = useState<Cell[]>([])
  const [activeCell, setActiveCell] = useState<string | null>(null)
  const [kernelStatus, setKernelStatus] = useState<'idle' | 'busy' | 'starting'>('idle')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [notebookName, setNotebookName] = useState('Untitled.ipynb')
  const [isDirty, setIsDirty] = useState(false)
  const [kernelId, setKernelId] = useState<string | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [wsConnectionStatus, setWsConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('disconnected')

  useEffect(() => {
    initializeNotebook()
  }, [])

  useEffect(() => {
    // Check connection and start kernel
    checkConnectionAndStartKernel()

    // Cleanup function to disconnect from kernel when component unmounts
    return () => {
      if (kernelId) {
        console.log('Cleaning up WebSocket connection for kernel:', kernelId)
        webSocketManager.disconnectFromKernel(kernelId)
      }
    }
  }, [])

  const initializeNotebook = async () => {
    try {
      setLoading(true)
      // Create a new notebook or load existing one
      const newNotebook: Notebook = {
        name: 'Untitled.ipynb',
        path: '/Untitled.ipynb',
        content: {
          cells: [
            {
              id: generateCellId(),
              type: 'code',
              content: '',
              output: [],
              execution_count: null,
              metadata: {}
            }
          ],
          metadata: {
            kernelspec: {
              display_name: 'Python 3',
              language: 'python',
              name: 'python3'
            }
          },
          nbformat: 4,
          nbformat_minor: 4
        }
      }
      
      setNotebook(newNotebook)
      setCells(newNotebook.content.cells)
      setActiveCell(newNotebook.content.cells[0].id)
    } catch (err) {
      setError('Failed to initialize notebook')
      console.error('Error initializing notebook:', err)
    } finally {
      setLoading(false)
    }
  }

  const generateCellId = () => {
    return `cell_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  const checkConnectionAndStartKernel = async () => {
    try {
      // First ensure we have XSRF token
      const hasToken = await JupyterAPI.ensureXSRFToken()
      if (!hasToken) {
        console.warn('Could not get XSRF token')
      }
      
      const isConnected = await JupyterAPI.checkConnection()
      setIsConnected(isConnected)
      
      if (isConnected) {
        // Get or create a kernel using the new method
        const kernelId = await JupyterAPI.getOrCreateKernel('python3')
        setKernelId(kernelId)
        setKernelStatus('idle')
        console.log('Kernel ready:', kernelId)
        
        // Check WebSocket connection status
        const wsStatus = webSocketManager.getConnectionStatus(kernelId)
        setWsConnectionStatus(wsStatus)
        console.log('WebSocket connection status:', wsStatus)
      }
    } catch (err) {
      console.error('Error checking connection:', err)
      setIsConnected(false)
    }
  }

  const addCell = (type: 'code' | 'markdown' = 'code', afterId?: string) => {
    const newCell: Cell = {
      id: generateCellId(),
      type,
      content: '',
      output: [],
      execution_count: null,
      metadata: {}
    }

    let newCells: Cell[]
    if (afterId) {
      const index = cells.findIndex(cell => cell.id === afterId)
      newCells = [...cells.slice(0, index + 1), newCell, ...cells.slice(index + 1)]
    } else {
      newCells = [...cells, newCell]
    }

    setCells(newCells)
    setActiveCell(newCell.id)
    setIsDirty(true)
  }

  const updateCell = (cellId: string, content: string) => {
    setCells(prev => prev.map(cell => 
      cell.id === cellId ? { ...cell, content } : cell
    ))
    setIsDirty(true)
  }

  const deleteCell = (cellId: string) => {
    setCells(prev => prev.filter(cell => cell.id !== cellId))
    setIsDirty(true)
  }

  const executeCell = async (cellId: string) => {
    const cell = cells.find(c => c.id === cellId)
    if (!cell || cell.type !== 'code') return

    if (!isConnected || !kernelId) {
      setError('Not connected to JupyterLab. Please check your connection.')
      return
    }

    try {
      setKernelStatus('busy')
      setError(null)
      
      // Debug: Log the execution attempt
      console.log('Executing cell:', { cellId, code: cell.content, kernelId })
      
      // Update cell to show execution
      setCells(prev => prev.map(c => 
        c.id === cellId 
          ? { ...c, execution_count: (c.execution_count || 0) + 1, output: [] }
          : c
      ))

      // Execute the cell using JupyterLab API
      const result = await JupyterAPI.executeCell(kernelId, cellId, cell.content)
      
      // Debug: Log the result
      console.log('Execution result:', result)
      
      // Process the WebSocket result
      const outputs = result.outputs || []
      const executionCount = result.execution_count || cell.execution_count || 1

      setCells(prev => prev.map(c => 
        c.id === cellId 
          ? { ...c, execution_count: executionCount, output: outputs }
          : c
      ))

    } catch (err) {
      setError('Failed to execute cell')
      console.error('Error executing cell:', err)
      
      // Add error output to cell
      setCells(prev => prev.map(c => 
        c.id === cellId 
          ? { 
              ...c, 
              output: [{
                output_type: 'error',
                ename: 'ExecutionError',
                evalue: err.message || 'Failed to execute code',
                traceback: []
              }]
            }
          : c
      ))
    } finally {
      setKernelStatus('idle')
    }
  }

  const executeAllCells = async () => {
    if (!isConnected || !kernelId) {
      setError('Not connected to JupyterLab. Please check your connection.')
      return
    }
    
    const codeCells = cells.filter(cell => cell.type === 'code')
    for (const cell of codeCells) {
      await executeCell(cell.id)
    }
  }

  const saveNotebook = async () => {
    try {
      if (isConnected) {
        // Save using JupyterLab API
        const notebookContent = {
          cells: cells.map(cell => ({
            cell_type: cell.type,
            source: [cell.content],
            metadata: cell.metadata,
            execution_count: cell.execution_count,
            outputs: cell.output || []
          })),
          metadata: notebook?.content.metadata || {},
          nbformat: 4,
          nbformat_minor: 4
        }
        
        await JupyterAPI.saveNotebook(`/${notebookName}`, notebookContent)
      }
      setIsDirty(false)
    } catch (err) {
      setError('Failed to save notebook')
      console.error('Error saving notebook:', err)
    }
  }

  const interruptKernel = async () => {
    if (!kernelId || !isConnected) return
    
    try {
      await JupyterAPI.interruptKernel(kernelId)
      setKernelStatus('idle')
    } catch (err) {
      console.error('Error interrupting kernel:', err)
    }
  }

  const restartKernel = async () => {
    if (!kernelId || !isConnected) return
    
    try {
      setKernelStatus('starting')
      await JupyterAPI.restartKernel(kernelId)
      setKernelStatus('idle')
      
      // Clear all cell outputs
      setCells(prev => prev.map(cell => ({
        ...cell,
        execution_count: null,
        output: []
      })))
    } catch (err) {
      console.error('Error restarting kernel:', err)
      setKernelStatus('idle')
    }
  }

  const renderCellOutput = (output: any[]) => {
    return output.map((out, index) => {
      if (out.output_type === 'execute_result') {
        return (
          <div key={index} className="bg-gray-50 p-3 rounded-md font-mono text-sm">
            <div className="text-gray-500 mb-1">Out[{out.execution_count}]:</div>
            <div>{out.data['text/plain']}</div>
          </div>
        )
      } else if (out.output_type === 'stream') {
        return (
          <div key={index} className="bg-gray-50 p-3 rounded-md font-mono text-sm">
            <div className="text-gray-500 mb-1">{out.name}:</div>
            <div>{out.text}</div>
          </div>
        )
      } else if (out.output_type === 'error') {
        return (
          <div key={index} className="bg-red-50 p-3 rounded-md font-mono text-sm">
            <div className="text-red-500 mb-1">Error:</div>
            <div className="text-red-700">{out.evalue}</div>
          </div>
        )
      }
      return null
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bits-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading notebook...</p>
        </div>
      </div>
    )
  }

  if (!isConnected) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 mb-4">
            <Settings size={48} className="mx-auto" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Not Connected to JupyterLab</h3>
          <p className="text-gray-600 mb-6">
            The notebook editor requires a connection to JupyterLab to execute Python code.
          </p>
          <div className="space-y-3">
            <button
              onClick={checkConnectionAndStartKernel}
              className="bits-btn-primary w-full"
            >
              <RotateCcw size={16} className="mr-2" />
              Retry Connection
            </button>
            <p className="text-sm text-gray-500">
              Make sure JupyterLab is running on port 8888
            </p>
            <button
              onClick={async () => {
                console.log('Manual refresh clicked')
                await checkConnectionAndStartKernel()
              }}
              className="bits-btn-secondary w-full mt-2"
            >
              <RotateCcw size={16} className="mr-2" />
              Refresh Connection
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Notebook Toolbar */}
      <div className="border-b border-gray-200 bg-gray-50 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => executeCell(activeCell!)}
            disabled={!activeCell || kernelStatus === 'busy'}
            className="bits-btn-primary text-sm"
            title="Run selected cells"
          >
            <Play size={16} className="mr-1" />
            Run
          </button>
          <button
            onClick={() => interruptKernel()}
            disabled={kernelStatus === 'idle' || !isConnected}
            className="bits-btn-secondary text-sm"
            title="Interrupt kernel"
          >
            <Square size={16} className="mr-1" />
            Stop
          </button>
          <button
            onClick={() => restartKernel()}
            disabled={!isConnected}
            className="bits-btn-secondary text-sm"
            title="Restart kernel"
          >
            <RotateCcw size={16} className="mr-1" />
            Restart
          </button>
          <button
            onClick={executeAllCells}
            disabled={kernelStatus === 'busy'}
            className="bits-btn-secondary text-sm"
            title="Run all cells"
          >
            <FastForward size={16} className="mr-1" />
            Run All
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <select className="border border-gray-300 rounded px-2 py-1 text-sm">
            <option>Code</option>
            <option>Markdown</option>
          </select>
          <div className="flex items-center space-x-1 text-sm text-gray-600">
            <div className={`w-2 h-2 rounded-full ${kernelStatus === 'idle' ? 'bg-green-500' : kernelStatus === 'busy' ? 'bg-yellow-500' : 'bg-gray-500'}`}></div>
            <span>Python 3 (ipykernel)</span>
            {!isConnected && (
              <span className="text-red-500 text-xs">(Disconnected)</span>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="border-b border-red-200 bg-red-50 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-red-500"></div>
              <span className="text-red-700 text-sm">{error}</span>
            </div>
            <button
              onClick={() => setError(null)}
              className="text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Notebook Name and Save */}
      <div className="border-b border-gray-200 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={notebookName}
            onChange={(e) => setNotebookName(e.target.value)}
            className="font-medium text-gray-900 bg-transparent border-none focus:outline-none"
          />
          {isDirty && <span className="text-orange-500 text-sm">*</span>}
        </div>
        <button
          onClick={saveNotebook}
          disabled={!isDirty}
          className="bits-btn-primary text-sm"
        >
          <Save size={16} className="mr-1" />
          Save
        </button>
      </div>

      {/* Cells */}
      <div className="flex-1 overflow-y-auto">
        {cells.map((cell, index) => (
          <div
            key={cell.id}
            className={`border-b border-gray-100 ${activeCell === cell.id ? 'bg-blue-50' : ''}`}
          >
            <div className="flex items-center justify-between px-4 py-2 bg-gray-50">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">[{index + 1}]</span>
                {cell.execution_count !== null && (
                  <span className="text-sm text-gray-500">In[{cell.execution_count}]:</span>
                )}
              </div>
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => executeCell(cell.id)}
                  disabled={cell.type !== 'code' || kernelStatus === 'busy'}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Run cell"
                >
                  <Play size={14} />
                </button>
                <button
                  onClick={() => addCell('code', cell.id)}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Add cell below"
                >
                  <Plus size={14} />
                </button>
                <button
                  onClick={() => deleteCell(cell.id)}
                  className="p-1 hover:bg-gray-200 rounded text-red-500"
                  title="Delete cell"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <textarea
                value={cell.content}
                onChange={(e) => updateCell(cell.id, e.target.value)}
                onFocus={() => setActiveCell(cell.id)}
                className="w-full min-h-[100px] p-3 border border-gray-300 rounded-md font-mono text-sm focus:outline-none focus:ring-2 focus:ring-bits-blue-500"
                placeholder={cell.type === 'code' ? '# Enter your code here...' : '# Enter markdown here...'}
              />
              
              {cell.output && cell.output.length > 0 && (
                <div className="mt-3">
                  {renderCellOutput(cell.output)}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Status Bar */}
      <div className="border-t border-gray-200 px-4 py-2 bg-gray-50 flex items-center justify-between text-sm text-gray-600">
        <div className="flex items-center space-x-4">
          <span>Simple</span>
          <span>Mode: Command</span>
          <span>0 $</span>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span>Python 3 (ipykernel) | {kernelStatus}</span>
            <div className={`w-2 h-2 rounded-full ${
              wsConnectionStatus === 'connected' ? 'bg-green-500' : 
              wsConnectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
            }`} title={`WebSocket: ${wsConnectionStatus}`}></div>
          </div>
          <span>Ln 1 Col 1</span>
          <span>{notebookName}</span>
        </div>
      </div>
    </div>
  )
}

export default CustomNotebook
