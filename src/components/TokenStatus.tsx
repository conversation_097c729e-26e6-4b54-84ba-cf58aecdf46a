import React, { useState, useEffect } from 'react'
import { Key, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { JupyterAPI } from '../services/jupyterAPI'

interface TokenStatusProps {
  onTokenChange: (token: string) => void
}

const TokenStatus: React.FC<TokenStatusProps> = ({ onTokenChange }) => {
  const [token, setToken] = useState<string>('')
  const [isValid, setIsValid] = useState<boolean | null>(null)
  const [isChecking, setIsChecking] = useState(false)

  useEffect(() => {
    checkTokenStatus()
  }, [])

  const checkTokenStatus = async () => {
    const currentToken = JupyterAPI.getToken()
    setToken(currentToken || '')
    
    if (currentToken) {
      setIsChecking(true)
      try {
        const valid = await JupyterAPI.testToken(currentToken)
        setIsValid(valid)
      } catch (error) {
        setIsValid(false)
      } finally {
        setIsChecking(false)
      }
    } else {
      setIsValid(false)
    }
  }

  const getStatusIcon = () => {
    if (isChecking) {
      return <AlertCircle className="w-4 h-4 text-yellow-500 animate-pulse" />
    }
    if (isValid === true) {
      return <CheckCircle className="w-4 h-4 text-green-500" />
    }
    if (isValid === false) {
      return <XCircle className="w-4 h-4 text-red-500" />
    }
    return <AlertCircle className="w-4 h-4 text-gray-400" />
  }

  const getStatusText = () => {
    if (isChecking) return 'Checking...'
    if (isValid === true) return 'Connected'
    if (isValid === false && token) return 'Invalid Token'
    return 'No Token'
  }

  const getStatusColor = () => {
    if (isChecking) return 'text-yellow-600'
    if (isValid === true) return 'text-green-600'
    if (isValid === false && token) return 'text-red-600'
    return 'text-gray-600'
  }

  return (
    <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg">
      <Key className="w-4 h-4 text-gray-500" />
      <div className="flex items-center space-x-1">
        {getStatusIcon()}
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>
      {token && (
        <div className="text-xs text-gray-500 font-mono">
          {token.substring(0, 8)}...
        </div>
      )}
    </div>
  )
}

export default TokenStatus
