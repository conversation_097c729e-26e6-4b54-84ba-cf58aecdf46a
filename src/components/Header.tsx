import React, { useState } from 'react'
import { useHub } from '../contexts/HubContext'
import { HubAPI } from '../services/hubAPI'
import { LogOut, User, Settings, HelpCircle, Key } from 'lucide-react'
import TokenStatus from './TokenStatus'
import TokenManager from './TokenManager'

const Header: React.FC = () => {
  const { user, logout } = useHub()
  const [showTokenManager, setShowTokenManager] = useState(false)
  const [currentToken, setCurrentToken] = useState('')

  const handleLogout = () => {
    logout()
  }

  const handleTokenSet = (token: string) => {
    setCurrentToken(token)
    // You can add additional logic here, like refreshing the notebook connection
  }

  return (
    <header className="bits-header">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white rounded flex items-center justify-center">
              <span className="text-bits-blue-500 font-bold text-sm">B</span>
            </div>
            <h1 className="text-xl font-bold text-white">BITS JupyterHub</h1>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Token Status and Management */}
          <div className="flex items-center space-x-2">
            <TokenStatus onTokenChange={handleTokenSet} />
            <button
              onClick={() => setShowTokenManager(true)}
              className="text-white hover:text-gray-200 flex items-center space-x-1 text-sm bg-white bg-opacity-20 px-3 py-1 rounded-lg transition-colors"
              title="Manage JupyterHub Token"
            >
              <Key size={16} />
              <span>Manage Token</span>
            </button>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <User size={16} className="text-white" />
            </div>
            <span className="text-white text-sm">{user?.name || 'User'}</span>
            <button
              onClick={handleLogout}
              className="text-white hover:text-gray-200 p-1"
              title="Logout"
            >
              <LogOut size={16} />
            </button>
          </div>
        </div>
      </div>
      
      {/* Token Manager Modal */}
      <TokenManager
        isOpen={showTokenManager}
        onClose={() => setShowTokenManager(false)}
        onTokenSet={handleTokenSet}
      />
    </header>
  )
}

export default Header
