import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useHub } from '../contexts/HubContext'
import { HubAPI } from '../services/hubAPI'
import { 
  BookOpen, 
  FileText, 
  Database, 
  Code, 
  Play, 
  Square,
  ChevronDown,
  ChevronRight,
  Plus
} from 'lucide-react'
import clsx from 'clsx'

const Sidebar: React.FC = () => {
  const { servers, startServer, stopServer } = useHub()
  const navigate = useNavigate()
  const [expandedSections, setExpandedSections] = useState({
    notebooks: true,
    datasets: true,
    scripts: true
  })

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section as keyof typeof prev]
    }))
  }

  const handleServerAction = async (serverName: string, action: 'start' | 'stop') => {
    if (action === 'start') {
      await startServer(serverName)
    } else {
      await stopServer(serverName)
    }
  }

  const handleOpenSection = (section: string) => {
    // Navigate to embedded notebook view for all sections
    navigate('/notebook/default')
  }

  const handleAddNew = (type: string) => {
    // Navigate to embedded notebook view for all actions
    navigate('/notebook/default')
  }

  return (
    <aside className="bits-sidebar">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Project Explorer</h2>
      </div>
      
      <div className="p-4">
        {/* Servers Section */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">Jupyter Servers</h3>
            <button 
              className="text-bits-blue-500 hover:text-bits-blue-600"
              onClick={() => startServer('default')}
            >
              <Plus size={16} />
            </button>
          </div>
          <div className="space-y-2">
            {servers.map((server) => (
              <div key={server.name} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm text-gray-700">{server.name}</span>
                <div className="flex items-center space-x-1">
                  {server.ready ? (
                    <button
                      onClick={() => handleServerAction(server.name, 'stop')}
                      className="p-1 text-red-500 hover:text-red-600"
                      title="Stop Server"
                    >
                      <Square size={14} />
                    </button>
                  ) : (
                    <button
                      onClick={() => handleServerAction(server.name, 'start')}
                      className="p-1 text-green-500 hover:text-green-600"
                      title="Start Server"
                    >
                      <Play size={14} />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Notebooks Section */}
        <div className="mb-4">
          <button
            onClick={() => toggleSection('notebooks')}
            className="flex items-center justify-between w-full text-left mb-2"
          >
            <div className="flex items-center space-x-2">
              <BookOpen size={16} className="text-bits-blue-500" />
              <span className="text-sm font-medium text-gray-700">Notebooks</span>
            </div>
            {expandedSections.notebooks ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </button>
          {expandedSections.notebooks && (
            <div className="ml-6 space-y-1">
              <div className="text-sm text-gray-500 py-1">No notebooks found</div>
              <button 
                onClick={() => handleAddNew('notebook')}
                className="text-sm text-bits-blue-500 hover:text-bits-blue-600 flex items-center space-x-1"
              >
                <Plus size={12} />
                <span>New Notebook</span>
              </button>
            </div>
          )}
        </div>

        {/* Datasets Section */}
        <div className="mb-4">
          <button
            onClick={() => toggleSection('datasets')}
            className="flex items-center justify-between w-full text-left mb-2"
          >
            <div className="flex items-center space-x-2">
              <Database size={16} className="text-bits-orange-500" />
              <span className="text-sm font-medium text-gray-700">Datasets</span>
            </div>
            {expandedSections.datasets ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </button>
          {expandedSections.datasets && (
            <div className="ml-6 space-y-1">
              <div className="text-sm text-gray-500 py-1">No datasets found</div>
              <button 
                onClick={() => handleAddNew('dataset')}
                className="text-sm text-bits-orange-500 hover:text-bits-orange-600 flex items-center space-x-1"
              >
                <Plus size={12} />
                <span>Upload Dataset</span>
              </button>
            </div>
          )}
        </div>

        {/* Scripts Section */}
        <div className="mb-4">
          <button
            onClick={() => toggleSection('scripts')}
            className="flex items-center justify-between w-full text-left mb-2"
          >
            <div className="flex items-center space-x-2">
              <Code size={16} className="text-green-500" />
              <span className="text-sm font-medium text-gray-700">Python Scripts</span>
            </div>
            {expandedSections.scripts ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </button>
          {expandedSections.scripts && (
            <div className="ml-6 space-y-1">
              <div className="text-sm text-gray-500 py-1">No scripts found</div>
              <button 
                onClick={() => handleAddNew('script')}
                className="text-sm text-green-500 hover:text-green-600 flex items-center space-x-1"
              >
                <Plus size={12} />
                <span>New Script</span>
              </button>
            </div>
          )}
        </div>

        {/* Quick Access */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <button 
            onClick={() => navigate('/notebook/default')}
            className="w-full bits-btn-primary text-sm"
          >
            <BookOpen size={16} className="mr-2" />
            Open Workspace
          </button>
        </div>
      </div>
    </aside>
  )
}

export default Sidebar
