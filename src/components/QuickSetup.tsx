import React from 'react'
import { Key, ExternalLink, Copy, CheckCircle } from 'lucide-react'

interface QuickSetupProps {
  onOpenTokenManager: () => void
}

const QuickSetup: React.FC<QuickSetupProps> = ({ onOpenTokenManager }) => {
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Key className="w-8 h-8 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Connect to JupyterHub</h2>
        <p className="text-gray-600">Get your API token to enable full functionality</p>
      </div>

      <div className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">Quick Setup (3 steps)</h3>
          <ol className="list-decimal list-inside space-y-2 text-blue-800 text-sm">
            <li>
              <button
                onClick={() => window.open('http://localhost:8888', '_blank')}
                className="flex items-center text-blue-600 hover:text-blue-800 underline"
              >
                <ExternalLink className="w-3 h-3 mr-1" />
                Open JupyterHub
              </button>
            </li>
            <li>Copy the token from the URL (after <code className="bg-blue-100 px-1 rounded">?token=</code>)</li>
            <li>
              <button
                onClick={onOpenTokenManager}
                className="text-blue-600 hover:text-blue-800 underline"
              >
                Configure token in settings
              </button>
            </li>
          </ol>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">What you'll get</h3>
          <ul className="space-y-1 text-sm text-gray-600">
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
              Real Python code execution
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
              Access to all JupyterLab features
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
              File browser and terminal access
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
              Kernel management
            </li>
          </ul>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => window.open('http://localhost:8888', '_blank')}
            className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
            <span>Open JupyterHub</span>
          </button>
          <button
            onClick={onOpenTokenManager}
            className="flex-1 flex items-center justify-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Key className="w-4 h-4" />
            <span>Configure Token</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default QuickSetup
