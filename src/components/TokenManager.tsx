import React, { useState, useEffect } from 'react'
import { Co<PERSON>, Check, ExternalLink, Refresh<PERSON><PERSON>, Setting<PERSON>, Key } from 'lucide-react'
import { JupyterAPI } from '../services/jupyterAPI'
import Notification from './Notification'

interface TokenManagerProps {
  isOpen: boolean
  onClose: () => void
  onTokenSet: (token: string) => void
}

const TokenManager: React.FC<TokenManagerProps> = ({ isOpen, onClose, onTokenSet }) => {
  const [currentToken, setCurrentToken] = useState<string>('')
  const [newToken, setNewToken] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null)
  const [copied, setCopied] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown')
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'warning'
    message: string
    isVisible: boolean
  }>({
    type: 'success',
    message: '',
    isVisible: false
  })

  useEffect(() => {
    if (isOpen) {
      loadCurrentToken()
      checkConnection()
    }
  }, [isOpen])

  const loadCurrentToken = () => {
    const token = JupyterAPI.getToken()
    setCurrentToken(token || '')
  }

  const checkConnection = async () => {
    setIsLoading(true)
    try {
      const isConnected = await JupyterAPI.checkConnection()
      setConnectionStatus(isConnected ? 'connected' : 'disconnected')
    } catch (error) {
      setConnectionStatus('disconnected')
    } finally {
      setIsLoading(false)
    }
  }

  const testToken = async (token: string) => {
    setIsTesting(true)
    setTestResult(null)
    try {
      const isValid = await JupyterAPI.testToken(token)
      setTestResult(isValid ? 'success' : 'error')
    } catch (error) {
      setTestResult('error')
    } finally {
      setIsTesting(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const openJupyterHub = () => {
    window.open('http://localhost:8888', '_blank')
  }

  const setToken = () => {
    if (newToken.trim()) {
      JupyterAPI.setToken(newToken.trim())
      setCurrentToken(newToken.trim())
      setNewToken('')
      onTokenSet(newToken.trim())
      showNotification('success', 'Token saved successfully!')
    }
  }

  const clearToken = () => {
    JupyterAPI.clearToken()
    setCurrentToken('')
    onTokenSet('')
    showNotification('warning', 'Token cleared')
  }

  const showNotification = (type: 'success' | 'error' | 'warning', message: string) => {
    setNotification({
      type,
      message,
      isVisible: true
    })
  }

  const hideNotification = () => {
    setNotification(prev => ({ ...prev, isVisible: false }))
  }

  const diagnoseConnection = async () => {
    await JupyterAPI.diagnoseConnection()
  }

  if (!isOpen) return null

  return (
    <>
      <Notification
        type={notification.type}
        message={notification.message}
        isVisible={notification.isVisible}
        onClose={hideNotification}
      />
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
              <Key className="mr-2 h-6 w-6 text-blue-600" />
              JupyterHub Token Manager
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Connection Status */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  connectionStatus === 'connected' ? 'bg-green-500' :
                  connectionStatus === 'disconnected' ? 'bg-red-500' : 'bg-yellow-500'
                }`} />
                <span className="font-medium">
                  JupyterHub Connection: {
                    connectionStatus === 'connected' ? 'Connected' :
                    connectionStatus === 'disconnected' ? 'Disconnected' : 'Checking...'
                  }
                </span>
              </div>
              <button
                onClick={checkConnection}
                disabled={isLoading}
                className="flex items-center text-blue-600 hover:text-blue-800 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>

          {/* Current Token */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-900">Current Token</h3>
            {currentToken ? (
              <div className="flex items-center space-x-2">
                <div className="flex-1 p-3 bg-gray-100 rounded-lg font-mono text-sm break-all">
                  {currentToken.substring(0, 20)}...
                </div>
                <button
                  onClick={() => copyToClipboard(currentToken)}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                  title="Copy token"
                >
                  {copied ? <Check className="w-4 h-4 text-green-600" /> : <Copy className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => testToken(currentToken)}
                  disabled={isTesting}
                  className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {isTesting ? 'Testing...' : 'Test'}
                </button>
                <button
                  onClick={clearToken}
                  className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Clear
                </button>
              </div>
            ) : (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800">No token configured. Please set a token to connect to JupyterHub.</p>
              </div>
            )}
            
            {testResult && (
              <div className={`mt-2 p-2 rounded-lg ${
                testResult === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
              }`}>
                {testResult === 'success' ? '✅ Token is valid!' : '❌ Token is invalid or expired'}
              </div>
            )}
          </div>

          {/* Get Token from JupyterHub */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-900">Get Token from JupyterHub</h3>
            <div className="space-y-3">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Step-by-step instructions:</h4>
                <ol className="list-decimal list-inside space-y-1 text-blue-800 text-sm">
                  <li>Click "Open JupyterHub" below to open JupyterHub in a new tab</li>
                  <li>Look at the URL in your browser - it should contain <code className="bg-blue-100 px-1 rounded">?token=...</code></li>
                  <li>Copy the entire token (the part after <code className="bg-blue-100 px-1 rounded">?token=</code>)</li>
                  <li>Paste it in the "New Token" field below</li>
                  <li>Click "Set Token" to save it</li>
                </ol>
              </div>
              
              <button
                onClick={openJupyterHub}
                className="flex items-center justify-center w-full p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open JupyterHub
              </button>
            </div>
          </div>

          {/* Set New Token */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-900">Set New Token</h3>
            <div className="flex space-x-2">
              <input
                type="text"
                value={newToken}
                onChange={(e) => setNewToken(e.target.value)}
                placeholder="Paste your JupyterHub token here..."
                className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={setToken}
                disabled={!newToken.trim()}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Set Token
              </button>
            </div>
          </div>

          {/* Advanced Options */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-900">Advanced Options</h3>
            <div className="space-y-2">
              <button
                onClick={diagnoseConnection}
                className="flex items-center w-full p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Settings className="w-4 h-4 mr-2" />
                Run Connection Diagnosis
              </button>
            </div>
          </div>

          {/* Help */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Need Help?</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Make sure JupyterHub is running on <code className="bg-gray-200 px-1 rounded">http://localhost:8888</code></li>
              <li>• The token is usually found in the URL when you open JupyterHub</li>
              <li>• Tokens are long strings of letters and numbers</li>
              <li>• If you can't find a token, try refreshing the JupyterHub page</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}

export default TokenManager
