import { Download, File, FileArchive, FileCode, FileImage, FileText, Folder, MoreVertical, Plus, Search, Trash2, Upload } from 'lucide-react'
import React, { useEffect, useState } from 'react'

interface FileItem {
  name: string
  path: string
  type: 'file' | 'directory'
  size?: number
  last_modified?: string
  content?: string
  mimetype?: string
}

const CustomFileBrowser: React.FC = () => {
  const [files, setFiles] = useState<FileItem[]>([])
  const [currentPath, setCurrentPath] = useState('/')
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showUpload, setShowUpload] = useState(false)
  const [uploadFile, setUploadFile] = useState<File | null>(null)

  useEffect(() => {
    loadFiles()
  }, [currentPath])

  const loadFiles = async () => {
    try {
      setLoading(true)
      // Mock file data - in real implementation, this would call JupyterLab API
      const mockFiles: FileItem[] = [
        {
          name: 'Untitled.ipynb',
          path: '/Untitled.ipynb',
          type: 'file',
          size: 1024,
          last_modified: new Date().toISOString(),
          mimetype: 'application/x-ipynb+json'
        },
        {
          name: 'data',
          path: '/data',
          type: 'directory',
          last_modified: new Date().toISOString()
        },
        {
          name: 'sample.csv',
          path: '/data/sample.csv',
          type: 'file',
          size: 2048,
          last_modified: new Date().toISOString(),
          mimetype: 'text/csv'
        },
        {
          name: 'analysis.py',
          path: '/analysis.py',
          type: 'file',
          size: 512,
          last_modified: new Date().toISOString(),
          mimetype: 'text/x-python'
        },
        {
          name: 'README.md',
          path: '/README.md',
          type: 'file',
          size: 256,
          last_modified: new Date().toISOString(),
          mimetype: 'text/markdown'
        }
      ]
      
      setFiles(mockFiles)
    } catch (err) {
      setError('Failed to load files')
      console.error('Error loading files:', err)
    } finally {
      setLoading(false)
    }
  }

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'directory') {
      return <Folder size={16} className="text-blue-500" />
    }
    
    const ext = file.name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'ipynb':
        return <FileText size={16} className="text-orange-500" />
      case 'py':
        return <FileCode size={16} className="text-green-500" />
      case 'csv':
      case 'json':
      case 'txt':
        return <FileText size={16} className="text-gray-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileImage size={16} className="text-purple-500" />
      case 'zip':
      case 'tar':
      case 'gz':
        return <FileArchive size={16} className="text-red-500" />
      default:
        return <File size={16} className="text-gray-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleFileClick = (file: FileItem) => {
    if (file.type === 'directory') {
      setCurrentPath(file.path)
    } else {
      setSelectedFile(file)
      // In real implementation, this would open the file in the appropriate editor
      console.log('Opening file:', file)
    }
  }

  const handleNewNotebook = () => {
    const newNotebook: FileItem = {
      name: `Untitled_${Date.now()}.ipynb`,
      path: `${currentPath}/Untitled_${Date.now()}.ipynb`,
      type: 'file',
      size: 0,
      last_modified: new Date().toISOString(),
      mimetype: 'application/x-ipynb+json'
    }
    setFiles(prev => [newNotebook, ...prev])
  }

  const handleNewFolder = () => {
    const newFolder: FileItem = {
      name: `New Folder_${Date.now()}`,
      path: `${currentPath}/New Folder_${Date.now()}`,
      type: 'directory',
      last_modified: new Date().toISOString()
    }
    setFiles(prev => [newFolder, ...prev])
  }

  const handleUpload = async () => {
    if (!uploadFile) return
    
    try {
      // In real implementation, this would upload to JupyterLab
      const newFile: FileItem = {
        name: uploadFile.name,
        path: `${currentPath}/${uploadFile.name}`,
        type: 'file',
        size: uploadFile.size,
        last_modified: new Date().toISOString(),
        mimetype: uploadFile.type
      }
      setFiles(prev => [newFile, ...prev])
      setUploadFile(null)
      setShowUpload(false)
    } catch (err) {
      setError('Failed to upload file')
      console.error('Error uploading file:', err)
    }
  }

  const handleDelete = (file: FileItem) => {
    setFiles(prev => prev.filter(f => f.path !== file.path))
    if (selectedFile?.path === file.path) {
      setSelectedFile(null)
    }
  }

  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const breadcrumbs = currentPath.split('/').filter(Boolean)

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 px-4 py-3 bg-gray-50">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">File Browser</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowUpload(true)}
              className="bits-btn-primary text-sm"
            >
              <Upload size={16} className="mr-1" />
              Upload
            </button>
            <button
              onClick={handleNewNotebook}
              className="bits-btn-secondary text-sm"
            >
              <Plus size={16} className="mr-1" />
              New Notebook
            </button>
            <button
              onClick={handleNewFolder}
              className="bits-btn-secondary text-sm"
            >
              <Folder size={16} className="mr-1" />
              New Folder
            </button>
          </div>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="border-b border-gray-200 px-4 py-2 bg-white">
        <div className="flex items-center space-x-1 text-sm">
          <button
            onClick={() => setCurrentPath('/')}
            className="text-bits-blue-500 hover:text-bits-blue-700"
          >
            Home
          </button>
          {breadcrumbs.map((crumb, index) => (
            <React.Fragment key={index}>
              <span className="text-gray-400">/</span>
              <button
                onClick={() => setCurrentPath('/' + breadcrumbs.slice(0, index + 1).join('/'))}
                className="text-bits-blue-500 hover:text-bits-blue-700"
              >
                {crumb}
              </button>
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Search */}
      <div className="border-b border-gray-200 px-4 py-2 bg-white">
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search files..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-bits-blue-500"
          />
        </div>
      </div>

      {/* File List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-bits-blue-500"></div>
          </div>
        ) : error ? (
          <div className="p-4 text-red-600">{error}</div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredFiles.map((file) => (
              <div
                key={file.path}
                className={`flex items-center justify-between px-4 py-3 hover:bg-gray-50 cursor-pointer ${
                  selectedFile?.path === file.path ? 'bg-blue-50' : ''
                }`}
                onClick={() => handleFileClick(file)}
              >
                <div className="flex items-center space-x-3 flex-1">
                  {getFileIcon(file)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {file.type === 'file' && file.size && formatFileSize(file.size)}
                      {file.last_modified && ` • ${formatDate(file.last_modified)}`}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      // Download functionality
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                    title="Download"
                  >
                    <Download size={14} />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDelete(file)
                    }}
                    className="p-1 hover:bg-gray-200 rounded text-red-500"
                    title="Delete"
                  >
                    <Trash2 size={14} />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      // More options
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                    title="More options"
                  >
                    <MoreVertical size={14} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Upload Modal */}
      {showUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Upload File</h3>
            <input
              type="file"
              onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
              className="w-full mb-4"
            />
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowUpload(false)
                  setUploadFile(null)
                }}
                className="bits-btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleUpload}
                disabled={!uploadFile}
                className="bits-btn-primary"
              >
                Upload
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomFileBrowser
