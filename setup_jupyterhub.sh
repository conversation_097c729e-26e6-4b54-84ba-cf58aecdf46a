#!/bin/bash

# BITS JupyterHub Setup Script
# This script installs and configures JupyterHub for the BITS frontend

echo "🚀 Setting up JupyterHub for BITS Frontend..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

echo "📦 Installing JupyterHub and dependencies..."

# Install JupyterHub
pip3 install jupyterhub

# Install JupyterLab
pip3 install jupyterlab

# Install additional dependencies
pip3 install notebook

echo "🔧 Creating JupyterHub configuration..."

# Create JupyterHub config directory
sudo mkdir -p /etc/jupyterhub

# Copy configuration file
sudo cp jupyterhub_config.py /etc/jupyterhub/

# Create cookie secret
openssl rand -hex 32 | sudo tee /etc/jupyterhub/cookie_secret

# Create database directory
sudo mkdir -p /var/lib/jupyterhub

# Set permissions
sudo chown -R $USER:$USER /var/lib/jupyterhub

echo "👤 Creating admin user..."

# Create admin user (you can change this)
sudo useradd -m -s /bin/bash admin
echo "admin:admin" | sudo chpasswd

echo "📁 Creating user directories..."

# Create notebook directories for users
sudo mkdir -p /home/<USER>/notebooks
sudo chown -R admin:admin /home/<USER>/notebooks

echo "🔒 Setting up SSL (optional)..."

# Generate self-signed certificate for HTTPS (optional)
# sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
#     -keyout /etc/jupyterhub/ssl.key \
#     -out /etc/jupyterhub/ssl.crt \
#     -subj "/C=IN/ST=State/L=City/O=BITS/CN=localhost"

echo "🚀 Starting JupyterHub..."

# Start JupyterHub
sudo jupyterhub -f /etc/jupyterhub/jupyterhub_config.py

echo "✅ JupyterHub setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. JupyterHub is running on http://localhost:8000"
echo "2. Login with username: admin, password: admin"
echo "3. Your BITS frontend should now connect to JupyterHub"
echo "4. Update vite.config.ts to point to http://localhost:8000"
echo ""
echo "🔧 To run JupyterHub as a service:"
echo "sudo systemctl enable jupyterhub"
echo "sudo systemctl start jupyterhub"
echo ""
echo "📖 For more information, see the README.md file"
